# دمج ميزة الموافقة على المهام في النموذج الأساسي - المنطق المبسط

## نظرة عامة

تم دمج ميزة الموافقة على المهام في النموذج الأساسي لإنشاء المهام (`AddTaskSheet.tsx`) مع منطق مبسط وواضح:

**المنطق الجديد:**
- **إذا فعّل المستخدم "تتطلب موافقة"** → ترسل المهمة للمسئول للموافقة عليها
- **إذا لم يفعّل الخيار** → تكون مهمة شخصية له فقط لتنظيم أشياء خاصة به

## التغييرات المنجزة

### 1. تعديل `AddTaskSheet.tsx`

#### إضافات جديدة:
- **حالة الموافقة المبسطة**: إضافة متغيرات حالة بسيطة
  ```typescript
  const [requiresApproval, setRequiresApproval] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState('');
  ```

- **منطق مبسط للأدوار**: تبسيط تحديد الصلاحيات
  ```typescript
  const isManager = userClaims?.isOrgOwner || userClaims?.isOrgAdmin || userClaims?.isOrgSupervisor;
  const canAssignTasks = isManager;
  ```

#### واجهة المستخدم المبسطة:
- **قسم خيارات الموافقة**: يظهر فقط للمستخدمين في المؤسسات والأقسام
- **خانة اختيار واحدة**: "تتطلب موافقة من المسئول قبل التنفيذ"
- **ملاحظات للمسئول**: حقل نص اختياري
- **معلومات إرشادية واضحة**: توضح الفرق بين المهام الشخصية ومهام الموافقة
- **رسالة توضيحية**: تشرح للمستخدم نوع المهام التي يمكنه إنشاؤها

#### منطق الحفظ المبسط:
- **مسارين واضحين**:
  - **مهام شخصية**: تُحفظ مباشرة كمهام فردية (`taskContext: 'individual'`)
  - **مهام تتطلب موافقة**: تُرسل للمسئول عبر وظيفة `createTaskWithApproval` دائماً على مستوى القسم

### 2. تحديث صفحة المهام (`page.tsx`)

#### إزالة المراجع:
- حذف استيراد `CreateTaskWithApproval`
- إزالة متغير حالة `isCreateApprovalTaskOpen`
- حذف مكون `CreateTaskWithApproval` من JSX

### 3. حذف الملفات غير المطلوبة

- **حذف**: `src/components/tasks/CreateTaskWithApproval.tsx`
- **الاحتفاظ**: `src/components/tasks/PendingApprovalTasks.tsx` (ما زال مطلوباً لعرض المهام المعلقة)

## المنطق المبسط الجديد

### 🎯 المبدأ الأساسي:
**خيار واحد بسيط:** "تتطلب موافقة من المسئول قبل التنفيذ"

### 📋 سيناريوهات الاستخدام:

#### 1. المهام الشخصية (الخيار الافتراضي):
- **متى**: عندما لا يفعّل المستخدم خيار الموافقة
- **النتيجة**: مهمة شخصية (`taskContext: 'individual'`)
- **الغرض**: تنظيم أشياء خاصة بالمستخدم
- **المثال**: "مراجعة التقارير الشهرية"، "تحديث المهارات"

#### 2. المهام التي تتطلب موافقة:
- **متى**: عندما يفعّل المستخدم خيار الموافقة
- **النتيجة**: ترسل للمسئول للموافقة عليها
- **الغرض**: مهام تخص القسم أو تحتاج موافقة إدارية
- **المثال**: "شراء معدات جديدة"، "تغيير إجراءات العمل"

### 🔧 الميزات الجديدة:

#### 1. واجهة موحدة ومبسطة
- نموذج واحد لجميع أنواع المهام
- خيار واحد واضح للموافقة
- رسائل توضيحية مفهومة

#### 2. منطق واضح ومباشر
- لا توجد تعقيدات في اختيار مستوى الموافقة
- المهام الشخصية دائماً فردية
- مهام الموافقة دائماً على مستوى القسم

#### 3. تحديث ديناميكي للواجهة
- نص الزر يتغير: "أضف المهمة" أو "إرسال للموافقة"
- رسائل النجاح توضح نوع المهمة المُنشأة
- أيقونات مختلفة حسب نوع المهمة

## كيفية الاستخدام

### للمهام الشخصية:
1. فتح نموذج إضافة مهمة
2. ملء تفاصيل المهمة
3. **عدم تفعيل** خيار "تتطلب موافقة من المسئول"
4. النقر على "أضف المهمة"
5. ستكون المهمة شخصية لك فقط

### للمهام التي تتطلب موافقة:
1. فتح نموذج إضافة مهمة
2. ملء تفاصيل المهمة
3. **تفعيل** خيار "تتطلب موافقة من المسئول قبل التنفيذ"
4. إضافة ملاحظات للمسئول (اختياري)
5. النقر على "إرسال للموافقة"
6. ستُرسل للمسئول للموافقة عليها

## الملفات المتأثرة

### معدلة:
- `src/components/AddTaskSheet.tsx`
- `src/app/(organization)/org/tasks/page.tsx`

### محذوفة:
- `src/components/tasks/CreateTaskWithApproval.tsx`

### غير متأثرة:
- `src/components/tasks/PendingApprovalTasks.tsx`
- `functions/src/tasks/approval.ts`
- جميع وظائف الخادم الخلفي للموافقة

## الفوائد

1. **تبسيط الكود**: تقليل عدد المكونات والملفات
2. **تحسين تجربة المستخدم**: واجهة موحدة وسهلة الاستخدام
3. **سهولة الصيانة**: كود أقل تعقيداً وأسهل في الصيانة
4. **مرونة أكبر**: إمكانية إضافة ميزات جديدة بسهولة

## ملاحظات مهمة

- جميع وظائف الموافقة في الخادم الخلفي تعمل كما هي
- مكون `PendingApprovalTasks` ما زال يعمل لعرض المهام المعلقة
- لا توجد تغييرات في قاعدة البيانات أو قواعد الأمان
- التوافق مع الإصدارات السابقة محفوظ
