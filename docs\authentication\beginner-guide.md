# دليل نظام التحقق من المستخدم - للمبتدئين

## 🎯 ما هو نظام التحقق؟

نظام يتأكد من هوية المستخدم ويحدد ما يمكنه فعله في التطبيق.

### مثال بسيط
- **المدير**: يمكنه إنشاء وحذف المهام
- **الموظف**: يمكنه فقط عرض المهام المكلف بها
- **الزائر**: لا يمكنه الدخول للتطبيق

## 🔑 كيف يعمل النظام؟

### الخطوة 1: تسجيل الدخول
```
المستخدم يدخل الإيميل وكلمة المرور
↓
Firebase يتحقق من البيانات
↓
إذا صحيحة: يعطي المستخدم "مفتاح دخول"
```

### الخطوة 2: تحديد الصلاحيات
```
النظام يبحث عن بيانات المستخدم
↓
يحدد نوع الحساب (فرد أم مؤسسة)
↓
يحدد الدور (مدير، موظف، فني، إلخ)
↓
يحدد الصلاحيات (ماذا يمكنه فعله)
```

### الخطوة 3: حماية الصفحات
```
المستخدم يحاول دخول صفحة
↓
النظام يتحقق: هل له صلاحية؟
↓
نعم: يعرض الصفحة
لا: يمنع الدخول
```

## 👥 أنواع المستخدمين

### 1. المستخدم الفردي
- **يستخدم التطبيق لنفسه**
- **لا ينتمي لمؤسسة**
- **يدير مهامه الشخصية فقط**

### 2. مستخدم المؤسسة
- **ينتمي لشركة أو مؤسسة**
- **له دور محدد (مدير، موظف، فني)**
- **يتعامل مع مهام المؤسسة**

## 🏢 أدوار المؤسسة

### مالك المؤسسة
- **أعلى صلاحية**
- **يدير كل شيء في المؤسسة**
- **يضيف ويحذف المستخدمين**

### مدير المؤسسة
- **يدير العمليات اليومية**
- **يشرف على الأقسام**
- **يراجع التقارير**

### المهندس
- **يدير المهام التقنية**
- **يشرف على الفنيين**
- **يراجع الأعمال**

### المشرف
- **يشرف على فريق العمل**
- **يتابع تنفيذ المهام**
- **يرفع التقارير للإدارة**

### الفني
- **ينفذ المهام المكلف بها**
- **يحدث حالة المهام**
- **يرى مهامه فقط**

### المساعد الفني
- **يساعد الفنيين**
- **ينفذ المهام البسيطة**
- **صلاحيات محدودة**

## 🛡️ كيف يحمي النظام البيانات؟

### الحماية من الأمام (الموقع)
```javascript
// مثال: إخفاء زر الحذف عن الموظفين
if (userRole === 'manager') {
  <DeleteButton />  // يظهر للمدير فقط
}
```

### الحماية من الخلف (الخادم)
```javascript
// مثال: التحقق قبل حذف المهمة
if (!user.canDelete) {
  return "غير مسموح لك بالحذف"
}
```

## 📱 أمثلة عملية

### مثال 1: إنشاء مهمة جديدة
```
1. المستخدم يضغط "إضافة مهمة"
2. النظام يتحقق: هل له صلاحية الإنشاء؟
3. إذا نعم: يفتح نموذج إنشاء المهمة
4. إذا لا: يظهر رسالة "غير مسموح"
```

### مثال 2: عرض قائمة المهام
```
1. المستخدم يدخل صفحة المهام
2. النظام يحدد نوع المستخدم:
   - مدير: يرى جميع المهام
   - فني: يرى مهامه فقط
   - زائر: لا يرى شيء
```

## ⚠️ مشاكل شائعة وحلولها

### المشكلة: "لا أستطيع الدخول"
**الحل:**
- تأكد من الإيميل وكلمة المرور
- تأكد من تفعيل الحساب
- امسح cache المتصفح

### المشكلة: "لا أرى بعض الأزرار"
**الحل:**
- هذا طبيعي - النظام يخفي ما لا تملك صلاحية له
- تواصل مع المدير لطلب صلاحيات إضافية

### المشكلة: "تظهر رسالة خطأ عند محاولة فعل شيء"
**الحل:**
- اقرأ رسالة الخطأ بعناية
- تأكد من صلاحياتك
- حدث الصفحة وحاول مرة أخرى

## 🔧 نصائح للاستخدام الآمن

### 1. كلمة المرور
- **استخدم كلمة مرور قوية**
- **لا تشاركها مع أحد**
- **غيرها بانتظام**

### 2. تسجيل الخروج
- **اخرج من الحساب عند الانتهاء**
- **خاصة على الأجهزة المشتركة**

### 3. الصلاحيات
- **لا تطلب صلاحيات لا تحتاجها**
- **أبلغ عن أي نشاط مشبوه**

## 📞 طلب المساعدة

### إذا واجهت مشكلة:
1. **اقرأ رسالة الخطأ**
2. **جرب الحلول المقترحة**
3. **تواصل مع الدعم التقني**
4. **اذكر تفاصيل المشكلة بوضوح**

---

**ملاحظة**: هذا النظام مصمم لحمايتك وحماية بيانات المؤسسة. الصلاحيات المحدودة هي ميزة أمان وليست عيب!

---
*دليل مبسط للمبتدئين - ديسمبر 2024*
