
/**
 * English language file
 */

const en = {
  // General
  general: {
    appName: "Task Intelligence",
    welcome: "Welcome to Task Management System",
    loading: "Loading...",
    saving: "Saving...",
    save: "Save",
    creating: "Creating...",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    create: "Create",
    search: "Search",
    filter: "Filter",
    filters: "Filters",
    applyFilters: "Apply Filters",
    resetFilters: "Reset Filters",
    noFiltersAvailable: "No filters available for this page",
    noResults: "No results",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    submit: "Submit",
    success: "Success",
    error: "Error",
    refresh: "Refresh",
    required: "Required",
    optional: "Optional",
    more: "More",
    less: "Less",
    all: "All",
    add: "Add",
    adding: "Adding...",
    updating: "Updating...",
    deleting: "Deleting...",
    saveChanges: "Save Changes",
    none: "None",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    now: "Now",
    date: "Date",
    time: "Time",
    datetime: "Date and Time",
    day: "Day",
    week: "Week",
    month: "Month",
    year: "Year",
    name: "Name",
    description: "Description",
    status: "Status",
    type: "Type",
    category: "Category",
    categories: "Categories",
    tags: "Tags",
    priority: "Priority",
    dueDate: "Due Date",
    createdAt: "Created At",
    updatedAt: "Updated At",
    assignedTo: "Assigned To",
    createdBy: "Created By",
    updatedBy: "Updated By",
    settings: "Settings",
    logout: "Logout",
    login: "Login",
    register: "Register",
    profile: "Profile",
    dashboard: "Dashboard",
    help: "Help",
    about: "About",
    contact: "Contact",
    language: "Language",
    theme: "Theme",
    dark: "Dark",
    light: "Light",
    system: "System",
    direction: "ltr",
    menu: "Toggle Sidebar",
    general: "General",
    features: "Features",
    errorLoadingPageContextOrUser: "Error loading page context or user."
  },

  sidebar: {
    mainMenu: "Main Menu",
    dashboard: "Dashboard",
    tasks: "Tasks",
    tasksTooltip: "View and manage tasks",
    dailyPlan: "Daily Plan",
    dailyPlanTooltip: "View suggested daily task plan",
    reports: "Reports",
    weeklyReports: "Weekly Reports",
    weeklyReportsTooltip: "View weekly reports",
    meetings: "Meetings",
    meetingsTooltip: "View and manage organization meetings",
    kpi: "KPIs",
    kpiTooltip: "View Key Performance Indicators",
    okr: "OKRs",
    okrTooltip: "Manage Objectives and Key Results for the organization",
    tools: "Tools",
    toolsTooltip: "Helper tools",
    smartSuggestions: "Smart Suggestions",
    smartSuggestionsTooltip: "View smart suggestions",
    adminMenu: "System Management",
    adminPanel: "Admin Panel",
    adminPanelTooltip: "Access admin control panel",
    users: "Users",
    usersTooltip: "Manage users and permissions",
    members: "Members",
    membersTooltip: "Manage organization members",
    departments: "Departments",
    departmentsTooltip: "Manage organization departments",
    organizationSettings: "Organization Settings",
    organizationSettingsTooltip: "Manage organization settings",
    dataManagement: "Data Management",
    dataManagementTooltip: "Manage system data (export/import)",
    importExport: "Import/Export Data",
    importExportTooltip: "Import and export individual user data",
    organizationDataManagement: "Org. Data Management",
    organizationDataManagementTooltip: "Export and import organization data",
    organizationRequests: "Organization Requests",
    organizationRequestsTooltip: "Review organization creation requests",
    generalSection: "General",
    settings: "Settings",
    settingsTooltip: "Modify application settings",
    documentation: "Documentation",
    documentationTooltip: "View system documentation",
    diagnostics: "Diagnostics",
    diagnosticsTooltip: "Diagnostic tools (Owner only)",
    userInfoTitle: "User Information",
    currentUserDefault: "Current User",
    userRoleLoading: "Loading...",
    signOut: "Sign Out",
    signOutTooltip: "Sign out of the account",
    toggleSidebar: "Toggle sidebar"
  },

  roles: {
    // System-wide roles (New is* pattern only)
    isSystemOwner: "System Owner",
    isSystemAdmin: "System Admin",
    isIndependent: "Independent User",

    // Organization roles
    isOrgOwner: "Organization Owner",
    isOrgAdmin: "Organization Admin",
    isOrgSupervisor: "Supervisor",
    isOrgEngineer: "Engineer",
    isOrgTechnician: "Technician",
    isOrgAssistant: "Assistant"
  },

  roleDescriptions: {
    // System-wide roles (New is* pattern only)
    isSystemOwner: "System Owner - Highest authority in the entire system, manages all systems and organizations",
    isSystemAdmin: "System Admin - Broad permissions to manage the system and organizations",
    isIndependent: "Independent User - Manages only their personal tasks and data",

    // Organization roles
    isOrgOwner: "Organization Owner - Full permissions within the organization",
    isOrgAdmin: "Organization Admin - Broad administrative permissions within the organization",
    isOrgSupervisor: "Supervisor - Supervises teams and tasks, manages operations",
    isOrgEngineer: "Engineer - Designs and plans projects and technical solutions",
    isOrgTechnician: "Technician - Executes technical tasks and maintenance",
    isOrgAssistant: "Assistant - Helps with simple tasks execution"
  },

  common: {
    applyFilters: "Apply Filters",
    filters: "Filters",
    resetFilters: "Reset Filters",
    noFiltersAvailable: "No filters available for this page"
  },

  // Authentication
  auth: {
    login: "Login",
    register: "Register",
    logout: "Logout",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    forgotPassword: "Forgot Password?",
    resetPassword: "Reset Password",
    changePassword: "Change Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    rememberMe: "Remember Me",
    loginWith: "Login with",
    registerWith: "Register with",
    alreadyHaveAccount: "Already have an account?",
    dontHaveAccount: "Don't have an account?",
    noAccount: "Don't have an account?",
    createAccount: "Create Account",
    verifyEmail: "Verify your email",
    verificationCode: "Verification Code",
    resendCode: "Resend Code",
    verificationEmailSent: "Verification email has been sent",
    passwordResetEmailSent: "Password reset email has been sent",
    passwordChanged: "Password changed successfully",
    invalidCredentials: "Invalid email or password",
    accountCreated: "Account created successfully",
    accountNotFound: "Account not found",
    accountDisabled: "Account is disabled",
    accountLocked: "Account is locked",
    tooManyAttempts: "Too many attempts, please try again later",
    sessionExpired: "Session expired, please login again",
    logoutConfirmation: "Are you sure you want to logout?",
    logoutSuccess: "Logged out successfully",
    loginDescription: "Welcome back! Log in to access your tasks.",
    registerDescription: "Join us to manage your tasks intelligently.",
    namePlaceholder: "Full Name",
    nameRequired: "Name is required",
    emailPlaceholder: "Email",
    passwordPlaceholder: "Password",
    confirmPasswordPlaceholder: "Confirm Password",
    confirmPasswordRequired: "Confirm password is required",
    passwordsDoNotMatch: "Passwords do not match",
    invalidEmail: "Invalid email",
    passwordMinLength: "Password must be at least {length} characters",
    loginFailed: "Login failed. Please check your credentials.",
    signupFailed: "Failed to create account. Email might already be in use.",
    resetPasswordFailed: "Failed to send password reset link.",
    resetPasswordDescription: "Enter your email to receive a password reset link.",
    backToLogin: "Back to Login",
    sendResetLink: "Send Reset Link",
    googleSignInFailed: "Failed to sign in with Google.",
    orContinueWith: "Or continue with",
    signInWithGoogle: "Sign in with Google",
    registrationSuccessful: "Registration Successful!",
    accountCreatedSuccessfully: "Your account has been created successfully",
    pleaseSignIn: "Please sign in to continue",
    redirectingToLogin: "Redirecting to login page...",
    goToLogin: "Go to Login",
  },

  // Suggestions
  suggestions: {
    smartSuggestions: "Smart Suggestions",
    errorLoadingSuggestions: "An error occurred while loading suggestions",
    errorLoadingTasks: "An error occurred while loading tasks",
    noSuggestions: "No Suggestions",
    generateNewSuggestion: "Generate a new suggestion using the buttons below. Suggestions are based on your current tasks.",
    currentUserTasks: "Current User Tasks ({count})",
    tasksDescription: "View of user tasks that will be used to generate suggestions.",
    noTasks: "No Tasks",
    noTasksDescription: "No tasks were found for the current user. You must create tasks first before generating suggestions.",
    showHideTasksList: "Show/Hide Tasks List ({count})",
    refreshTasksList: "Refresh Tasks List",
    showLastAiResponse: "Show Last AI JSON Response (Owner Only)",
    jsonResponse: "JSON Response",
  },

  // Tasks
  tasks: {
    tasks: "Tasks",
    task: "Task",
    myTasks: "My Tasks",
    assignedTasks: "Assigned Tasks",
    allTasks: "All Tasks",
    newTask: "New Task",
    editTask: "Edit Task",
    deleteTask: "Delete Task",
    deleteTaskConfirmation: "Are you sure you want to delete this task?",
    deleteTaskWarning: "This action cannot be undone. This will permanently delete your task.",
    taskDeleted: "Task deleted successfully",
    taskDeletedSuccessfully: "Task has been deleted successfully.",
    taskDeleteError: "Error deleting task",
    errorDeletingTask: "An error occurred while trying to delete the task.",
    taskCreated: "Task created successfully",
    taskUpdated: "Task updated successfully",
    taskDetails: "Task Details",
    taskName: "Task Name",
    taskDescription: "Task Description",
    taskStatus: "Task Status",
    taskPriority: "Task Priority",
    taskDueDate: "Task Due Date",
    taskCategory: "Task Category",
    taskAssignee: "Task Assignee",
    taskCreator: "Task Creator",
    taskCreatedAt: "Task Created At",
    taskUpdatedAt: "Task Updated At",
    taskComments: "Task Comments",
    taskAttachments: "Task Attachments",
    taskTags: "Task Tags",
    taskSubtasks: "Subtasks",
    taskParent: "Parent Task",
    taskDependencies: "Task Dependencies",
    taskBlocking: "Blocking Tasks",
    taskBlockedBy: "Blocked By Tasks",
    taskEstimatedTime: "Estimated Time",
    taskActualTime: "Actual Time",
    taskTimeSpent: "Time Spent",
    taskTimeRemaining: "Time Remaining",
    taskProgress: "Task Progress",
    taskNotes: "Task Notes",
    taskHistory: "Task History",
    taskActivities: "Task Activities",
    taskWatchers: "Watchers",
    addWatcher: "Add Watcher",
    removeWatcher: "Remove Watcher",
    watchTask: "Watch Task",
    unwatchTask: "Unwatch Task",
    assignToMe: "Assign to Me",
    unassign: "Unassign",
    markAsComplete: "Mark as Complete",
    markAsIncomplete: "Mark as Incomplete",
    overdue: "Overdue",
    dueToday: "Due Today",
    dueSoon: "Due Soon",
    noDueDate: "No Due Date",
    highPriority: "High Priority",
    mediumPriority: "Medium Priority",
    lowPriority: "Low Priority",
    noPriority: "No Priority",
    statusNew: "New",
    statusInProgress: "In Progress",
    statusOnHold: "On Hold",
    statusCompleted: "Completed",
    statusCancelled: "Cancelled",
    filterByStatus: "Filter by Status",
    filterByPriority: "Filter by Priority",
    filterByAssignee: "Filter by Assignee",
    filterByDueDate: "Filter by Due Date",
    filterByCategory: "Filter by Category",
    sortByDueDate: "Sort by Due Date",
    sortByPriority: "Sort by Priority",
    sortByStatus: "Sort by Status",
    sortByName: "Sort by Name",
    sortByCreatedAt: "Sort by Created At",
    sortByUpdatedAt: "Sort by Updated At",
    noTasks: "No Tasks",
    noTasksFound: "No Tasks Found",
    noTasksCreated: "No Tasks Created Yet",
    createFirstTask: "Create First Task",
    noTasksToDisplay: "No tasks to display.",
    useButtonToAddNewTask: "Use the {button} button above to add a new task!",
    noTasksMatchCurrentFilters: "No tasks match the current filters.",
    removeFilters: "Remove Filters",
    noTasksInCategory: "No tasks in this category {category} {dateRange}.",
    forCategory: "for category \"{category}\"",
    withinSpecifiedDateRange: "within the specified date range",
    cannotUpdateStatus: "Cannot update status.",
    statusUpdated: "Status Updated",
    taskStatusUpdatedTo: "Task status updated to {status}.",
    statusUpdateError: "Status Update Error",
    errorUpdatingTaskStatus: "An error occurred while updating the task status.",
    invalidTaskIdOrNotLoggedIn: "Invalid task ID or not logged in.",
    taskMoved: "Task Moved",
    taskMovedTo: "Task moved to {category}.",
    taskMoveError: "Task Move Error",
    errorMovingTask: "An error occurred while moving the task.",
    category: {
      overdue: "Overdue",
      today: "Today",
      upcoming: "Upcoming",
      scheduled: "Scheduled",
      pending: "Pending",
      hold: "On Hold",
      completed: "Completed"
    }
  },

  // Reports
  reports: {
    reports: "Reports",
    report: "Report",
    dailyPlan: "Daily Plan",
    weeklyReport: "Weekly Report",
    monthlyReport: "Monthly Report",
    customReport: "Custom Report",
    generateReport: "Generate Report",
    reportGenerated: "Report generated successfully",
    reportPeriod: "Report Period",
    reportType: "Report Type",
    reportFormat: "Report Format",
    reportData: "Report Data",
    reportFilters: "Report Filters",
    reportSummary: "Report Summary",
    reportDetails: "Report Details",
    reportCharts: "Report Charts",
    reportTables: "Report Tables",
    selectPeriod: "Select Period",
    selectReportType: "Select Report Type",
    quarter: "Quarter",
    kpiType: "KPI Type",
    selectKpiType: "Select KPI Type",
    allKpis: "All KPIs",
    completionRate: "Completion Rate",
    byPriority: "By Priority",
    exportReport: "Export Report",
    printReport: "Print Report",
    shareReport: "Share Report",
    saveReport: "Save Report",
    savedReports: "Saved Reports",
    noReports: "No Reports",
    noReportsFound: "No Reports Found",
    noReportsCreated: "No Reports Created Yet",
    createFirstReport: "Create First Report",
    tasksCompleted: "Tasks Completed",
    tasksInProgress: "Tasks In Progress",
    tasksOverdue: "Tasks Overdue",
    tasksDueToday: "Tasks Due Today",
    tasksDueSoon: "Tasks Due Soon",
    tasksCreated: "Tasks Created",
    tasksAssigned: "Tasks Assigned",
    tasksUpdated: "Tasks Updated",
    timeSpent: "Time Spent",
    timeRemaining: "Time Remaining",
    productivity: "Productivity",
    efficiency: "Efficiency",
    performance: "Performance",
    progress: "Progress",
    trends: "Trends",
    comparisons: "Comparisons",
    statistics: "Statistics",
    metrics: "Metrics",
    kpis: "Key Performance Indicators",
  },

  // Notifications
  notifications: {
    notifications: "Notifications",
    notificationsTooltip: "View notifications",
    notification: "Notification",
    notificationSettings: "Notification Settings",
    notificationSettingsDescription: "Manage notification settings and alert preferences",
    customizeNotifications: "Customize how you receive notifications and their types",
    manageNotifications: "Manage Notifications",
    markAllAsRead: "Mark All as Read",
    markAsRead: "Mark as Read",
    markAsUnread: "Mark as Unread",
    deleteNotification: "Delete Notification",
    deleteAllNotifications: "Delete All Notifications",
    notificationDeleted: "Notification deleted successfully",
    allNotificationsDeleted: "All notifications deleted successfully",
    allNotificationsMarkedAsRead: "All notifications marked as read",
    noNotifications: "No Notifications",
    noNotificationsFound: "No Notifications Found",
    noUnreadNotifications: "No Unread Notifications",
    enableNotifications: "Enable Notifications",
    disableNotifications: "Disable Notifications",
    notificationsEnabled: "Notifications enabled",
    notificationsDisabled: "Notifications disabled",
    pushNotifications: "Push Notifications",
    emailNotifications: "Email Notifications",
    inAppNotifications: "In-App Notifications",
    receiveInAppNotifications: "Receive notifications within the application",
    receiveEmailNotifications: "Receive notifications via email",
    receiveAiSuggestions: "Receive smart suggestions to improve productivity",
    selectEmailFrequency: "Select email frequency",
    notifyOnTaskAssigned: "Notify on Task Assigned",
    notifyOnTaskUpdated: "Notify on Task Updated",
    notifyOnTaskCompleted: "Notify on Task Completed",
    notifyOnTaskOverdue: "Notify on Task Overdue",
    notifyOnTaskComment: "Notify on Task Comment",
    notifyOnMention: "Notify on Mention",
    notifyOnSystemUpdates: "Notify on System Updates",
    notificationFrequency: "Notification Frequency",
    immediate: "Immediate",
    hourly: "Hourly",
    daily: "Daily",
    weekly: "Weekly",
    never: "Never",
    doNotDisturb: "Do Not Disturb",
    doNotDisturbDescription: "Set a time period when you don't want to receive notifications",
    enableDoNotDisturb: "Enable Do Not Disturb",
    doNotDisturbFrom: "From",
    doNotDisturbTo: "To",
    unread: "Unread",
    read: "Read",
    all: "All",
    taskNotifications: "Task Notifications",
    taskNotificationsDescription: "Task creation, assignment, status changes",
    systemNotifications: "System Notifications",
    systemNotificationsDescription: "System updates, member additions, department creation",
    meetingNotifications: "Meeting Notifications",
    meetingNotificationsDescription: "Meeting creation, reminders, updates",
    aiSuggestions: "AI Suggestions",
    notificationTypes: "Notification Types",
    chooseNotificationTypes: "Choose which types of notifications you want to receive",
    generalSettings: "General Settings",
    pleaseLoginToView: "Please login to view notification settings.",
    settingsSaved: "Notification settings saved successfully",
    errorLoadingSettings: "Error loading notification settings",
    errorSavingSettings: "Error saving notification settings",
  },

  // Organization
  organization: {
    organization: "Organization",
    organizations: "Organizations",
    organizationSettings: "Organization Settings",
    organizationMembers: "Organization Members",
    organizationDepartments: "Organization Departments",
    organizationRoles: "Organization Roles",
    organizationPermissions: "Organization Permissions",
    organizationInvitations: "Organization Invitations",
    organizationRequests: "Organization Requests",
    organizationDataNotFound: "Organization data not found",
    errorFetchingOrganizationSettings: "Error fetching organization settings",
    organizationSettingsSaved: "Organization settings saved successfully",
    errorSavingOrganizationSettings: "Error saving organization settings",
    basicInformation: "Basic Information",
    basicInformationDescription: "Basic information about the organization",
    permissionsSettings: "Permissions Settings",
    permissionsSettingsDescription: "Control organization member permissions",
    allowMemberInvites: "Allow Member Invites",
    allowMemberInvitesDescription: "Allow administrators to invite new members to the organization",
    allowDepartmentCreation: "Allow Department Creation",
    allowDepartmentCreationDescription: "Allow administrators to create new departments in the organization",
    requireTaskApproval: "Require Task Approval",
    requireTaskApprovalDescription: "Require administrator approval for new tasks before publishing",
    advancedFeaturesDescription: "Enable or disable advanced features in the system",
    aiFeatures: "AI Features",
    aiFeaturesDescription: "Enable AI features such as task suggestions and report generation",
    createOrganization: "Create Organization",
    editOrganization: "Edit Organization",
    deleteOrganization: "Delete Organization",
    leaveOrganization: "Leave Organization",
    joinOrganization: "Join Organization",
    switchOrganization: "Switch Organization",
    organizationName: "Organization Name",
    organizationDescription: "Organization Description",
    organizationLogo: "Organization Logo",
    organizationWebsite: "Organization Website",
    organizationEmail: "Organization Email",
    organizationPhone: "Organization Phone",
    organizationAddress: "Organization Address",
    organizationCreatedAt: "Organization Created At",
    organizationUpdatedAt: "Organization Updated At",
    organizationOwner: "Organization Owner",
    organizationAdmin: "Organization Admin",
    organizationMember: "Organization Member",
    organizationGuest: "Organization Guest",
    addMember: "Add Member",
    removeMember: "Remove Member",
    inviteMember: "Invite Member",
    acceptInvitation: "Accept Invitation",
    rejectInvitation: "Reject Invitation",
    cancelInvitation: "Cancel Invitation",
    resendInvitation: "Resend Invitation",
    memberAdded: "Member added successfully",
    memberRemoved: "Member removed successfully",
    invitationSent: "Invitation sent successfully",
    invitationAccepted: "Invitation accepted successfully",
    invitationRejected: "Invitation rejected successfully",
    invitationCancelled: "Invitation cancelled successfully",
    invitationResent: "Invitation resent successfully",
    noOrganizations: "No Organizations",
    noOrganizationsFound: "No Organizations Found",
    noOrganizationsCreated: "No Organizations Created Yet",
    createFirstOrganization: "Create First Organization",
    noMembers: "No Members",
    noMembersFound: "No Members Found",
    noInvitations: "No Invitations",
    noInvitationsFound: "No Invitations Found",
    noDepartments: "No Departments",
    noDepartmentsFound: "No Departments Found",
    createDepartment: "Create Department",
    editDepartment: "Edit Department",
    deleteDepartment: "Delete Department",
    departmentName: "Department Name",
    departmentDescription: "Department Description",
    departmentHead: "Department Head",
    departmentMembers: "Department Members",
    addToDepartment: "Add to Department",
    removeFromDepartment: "Remove from Department",
    departmentCreated: "Department created successfully",
    departmentUpdated: "Department updated successfully",
    departmentDeleted: "Department deleted successfully",
    memberAddedToDepartment: "Member added to department successfully",
    memberRemovedFromDepartment: "Member removed from department successfully",

    // 🏢 Departments
    departments: "Departments",
    department: "Department",
    departmentsManagement: "Departments Management",
    createNewDepartment: "Create New Department",
    departmentDetails: "Department Details",
    departmentStatistics: "Department Statistics",
    departmentTasks: "Department Tasks",
    departmentMeetings: "Department Meetings",
    noDepartmentDescription: "No department description",
    departmentCreatedSuccessfully: "Department created successfully",
    errorCreatingDepartment: "Error creating department",
    errorFetchingDepartments: "Error fetching departments",

    // 👥 Individuals
    individuals: "Individuals",
    individual: "Individual",
    individualsInOrganization: "Individuals in Organization",
    membersWithoutDepartment: "Members without Department",
    unassignedMembers: "Unassigned Members",
    assignToDepartment: "Assign to Department",
    noIndividuals: "No Individuals",
    noIndividualsInOrganization: "No individuals in organization",
    allMembersAssigned: "All members are assigned to departments",

    // 📊 Additional keys for members and departments
    member: "Member",
    departmentFormDescription: "Enter the new department information. Click save when finished.",
    canAssignToDepartment: "These members can be assigned to departments",
    noDepartmentMembers: "No members in departments",
    assignMembersToDepartments: "Assign members to departments",
    membersAssignedToDepartments: "Members assigned to organization departments",
    unknownDepartment: "Unknown Department",
    unassigned: "Unassigned",
    addMemberDescription: "Enter the member's email and role in the organization",
    selectRole: "Select Role",
    selectDepartment: "Select Department",
    noDepartment: "No Department",
    editMember: "Edit Member",
    editMemberDescription: "Edit member's role and department in the organization",
    confirmDeleteMember: "Are you sure you want to delete this member?",
    deleteMemberWarning: "The member will be removed from the organization. This action cannot be undone",
    noOrganizationAccess: "No Organization Access",
    noOrganizationAccessDescription: "You must be a member of an organization to access this page",
    goToOrganization: "Go to Organization",
  },

  // Meetings
  meetings: {
    meetings: "Meetings",
    meeting: "Meeting",
    createMeeting: "Create Meeting",
    editMeeting: "Edit Meeting",
    deleteMeeting: "Delete Meeting",
    meetingTitle: "Meeting Title",
    meetingDescription: "Meeting Description",
    meetingDate: "Meeting Date",
    meetingTime: "Meeting Time",
    meetingLocation: "Meeting Location",
    meetingParticipants: "Meeting Participants",
    meetingAgenda: "Meeting Agenda",
    meetingNotes: "Meeting Notes",
    meetingDecisions: "Meeting Decisions",
    meetingTasks: "Meeting Tasks",
    meetingStatus: "Meeting Status",
    meetingType: "Meeting Type",
    noMeetings: "No Meetings",
    upcomingMeetings: "Upcoming Meetings",
    pastMeetings: "Past Meetings",
    todayMeetings: "Today's Meetings",
  },

  // Settings
  settings: {
    settings: "Settings",
    generalSettings: "General Settings",
    accountSettings: "Account Settings",
    profileSettings: "Profile Settings",
    securitySettings: "Security Settings",
    notificationSettings: "Notification Settings",
    appearanceSettings: "Appearance Settings",
    languageSettings: "Language Settings",
    themeSettings: "Theme Settings",
    privacySettings: "Privacy Settings",
    advancedSettings: "Advanced Settings",
    experimentalSettings: "Experimental Settings",
    developerSettings: "Developer Settings",
    systemSettings: "System Settings",
    saveSettings: "Save Settings",
    resetSettings: "Reset Settings",
    exportSettings: "Export Settings",
    importSettings: "Import Settings",
    features: "Features",
    section: "Section",
    selectSection: "Select Section",
    settingsSaved: "Settings saved successfully",
    settingsReset: "Settings reset successfully",
    settingsExported: "Settings exported successfully",
    settingsImported: "Settings imported successfully",
    appearance: "Appearance",
    language: "Language",
    theme: "Theme",
    darkMode: "Dark Mode",
    lightMode: "Light Mode",
    systemMode: "System Mode",
    fontSize: "Font Size",
    fontFamily: "Font Family",
    colorScheme: "Color Scheme",
    dateFormat: "Date Format",
    timeFormat: "Time Format",
    timezone: "Timezone",
    firstDayOfWeek: "First Day of Week",
    currency: "Currency",
    measurementUnit: "Measurement Unit",
    notifications: "Notifications",
    privacy: "Privacy",
    security: "Security",
    twoFactorAuth: "Two-Factor Authentication",
    changePassword: "Change Password",
    deleteAccount: "Delete Account",
    dataExport: "Data Export",
    dataImport: "Data Import",
    dataDeletion: "Data Deletion",
    cookies: "Cookies",
    termsOfService: "Terms of Service",
    privacyPolicy: "Privacy Policy",
    about: "About",
    help: "Help",
    feedback: "Feedback",
    contactSupport: "Contact Support",
    reportBug: "Report Bug",
    requestFeature: "Request Feature",
    version: "Version",
    buildNumber: "Build Number",
    releaseDate: "Release Date",
    licenseInfo: "License Information",
    thirdPartyLibraries: "Third-Party Libraries",
    acknowledgements: "Acknowledgements",
  },

  // Tools
  tools: {
    tools: "Tools",
    calculator: "Calculator",
    calendar: "Calendar",
    notes: "Notes",
    timer: "Timer",
    stopwatch: "Stopwatch",
    alarm: "Alarm",
    reminders: "Reminders",
    weather: "Weather",
    maps: "Maps",
    translator: "Translator",
    converter: "Converter",
    scanner: "Scanner",
    recorder: "Recorder",
    camera: "Camera",
    aiTools: "AI Tools",
    aiSuggestions: "AI Suggestions",
    aiTaskSuggestions: "AI Task Suggestions",
    aiPrioritization: "AI Prioritization",
    aiScheduling: "AI Scheduling",
    aiAnalytics: "AI Analytics",
    aiAssistant: "AI Assistant",
    aiChatbot: "AI Chatbot",
    aiSummary: "AI Summary",
    aiTranslation: "AI Translation",
    aiVoiceAssistant: "AI Voice Assistant",
    aiImageGeneration: "AI Image Generation",
    aiTextGeneration: "AI Text Generation",
    aiCodeGeneration: "AI Code Generation",
    aiDataAnalysis: "AI Data Analysis",
    aiPrediction: "AI Prediction",
    aiRecommendation: "AI Recommendation",
    aiPersonalization: "AI Personalization",
    aiOptimization: "AI Optimization",
    aiAutomation: "AI Automation",
    underDevelopment: "Under Development",
    aiPrompts: "AI Prompts",
    promptTitle: "Prompt Title",
    promptDescription: "Prompt Description",
    promptContent: "Prompt Content",
    promptCategory: "Prompt Category",
    promptTags: "Prompt Tags",
    promptCreator: "Prompt Creator",
    promptCreatedAt: "Prompt Created At",
    promptUpdatedAt: "Prompt Updated At",
    promptUsageCount: "Prompt Usage Count",
    promptRating: "Prompt Rating",
    promptFeedback: "Prompt Feedback",
    promptShared: "Prompt Shared",
    promptPrivate: "Private Prompt",
    promptPublic: "Public Prompt",
    createPrompt: "Create Prompt",
    editPrompt: "Edit Prompt",
    deletePrompt: "Delete Prompt",
    usePrompt: "Use Prompt",
    sharePrompt: "Share Prompt",
    savePrompt: "Save Prompt",
    copyPrompt: "Copy Prompt",
    favoritePrompt: "Favorite Prompt",
    unfavoritePrompt: "Unfavorite Prompt",
    promptDeleted: "Prompt deleted successfully",
    promptCreated: "Prompt created successfully",
    promptUpdated: "Prompt updated successfully",
    promptCopied: "Prompt copied successfully",
    promptSharedSuccess: "Prompt shared successfully",
    promptSaved: "Prompt saved successfully",
    noPrompts: "No Prompts",
    noPromptsFound: "No Prompts Found",
    noPromptsCreated: "No Prompts Created Yet",
    createFirstPrompt: "Create First Prompt",
    myPrompts: "My Prompts",
    sharedPrompts: "Shared Prompts",
    publicPrompts: "Public Prompts",
    favoritePrompts: "Favorite Prompts",
    recentPrompts: "Recent Prompts",
    popularPrompts: "Popular Prompts",
    promptLibrary: "Prompt Library",
    importPrompts: "Import Prompts",
    exportPrompts: "Export Prompts",
    searchPrompts: "Search Prompts",
    filterPromptsByCategory: "Filter Prompts by Category",
    sortPromptsByDate: "Sort Prompts by Date",
    sortPromptsByRating: "Sort Prompts by Rating",
    sortPromptsByUsage: "Sort Prompts by Usage",
  },

  // Data Management
  dataManagement: {
    dataManagement: "Data Management",
    importData: "Import Data",
    exportData: "Export Data",
    backupData: "Backup Data",
    restoreData: "Restore Data",
    deleteData: "Delete Data",
    dataImported: "Data imported successfully",
    dataExported: "Data exported successfully",
    dataBackedUp: "Data backed up successfully",
    dataRestored: "Data restored successfully",
    dataDeleted: "Data deleted successfully",
    importFromFile: "Import from file",
    exportToFile: "Export to file",
    selectFileToImport: "Select file to import",
    selectDataToExport: "Select data to export",
    confirmDataDeletion: "Confirm data deletion",
    dataImportError: "Error importing data",
    dataExportError: "Error exporting data",
    dataBackupError: "Error backing up data",
    dataRestoreError: "Error restoring data",
    dataDeleteError: "Error deleting data",
    dataType: "Data Type",
    selectDataType: "Select Data Type",
  },

  // Errors
  errors: {
    error: "Error",
    warning: "Warning",
    info: "Information",
    success: "Success",
    somethingWentWrong: "Something went wrong",
    pageNotFound: "Page not found",
    accessDenied: "Access denied",
    unauthorized: "Unauthorized",
    forbidden: "Forbidden",
    notFound: "Not found",
    badRequest: "Bad request",
    serverError: "Server error",
    networkError: "Network error",
    connectionError: "Connection error",
    timeoutError: "Timeout error",
    validationError: "Validation error",
    authenticationError: "Authentication error",
    authorizationError: "Authorization error",
    databaseError: "Database error",
    fileError: "File error",
    uploadError: "Upload error",
    downloadError: "Download error",
    importError: "Import error",
    exportError: "Export error",
    printError: "Print error",
    parseError: "Parse error",
    formatError: "Format error",
    syntaxError: "Syntax error",
    typeError: "Type error",
    rangeError: "Range error",
    referenceError: "Reference error",
    internalError: "Internal error",
    unknownError: "Unknown error",
    tryAgain: "Try again",
    refresh: "Refresh",
    goBack: "Go back",
    goHome: "Go home",
    contactAdmin: "Contact administrator",
    seeDetails: "See details",
    hideDetails: "Hide details",
    errorCode: "Error code",
    errorMessage: "Error message",
    errorDetails: "Error details",
    errorStack: "Error stack",
    errorTime: "Error time",
    errorLocation: "Error location",
    errorUser: "Error user",
    errorSession: "Error session",
    errorBrowser: "Error browser",
    errorOS: "Error OS",
    errorDevice: "Error device",
    errorIP: "Error IP",
    errorURL: "Error URL",
    errorMethod: "Error method",
    errorStatus: "Error status",
    errorResponse: "Error response",
    errorRequest: "Error request",
    errorHeaders: "Error headers",
    errorBody: "Error body",
    errorQuery: "Error query",
    errorParams: "Error params",
    errorData: "Error data",
  },
};

export default en;
