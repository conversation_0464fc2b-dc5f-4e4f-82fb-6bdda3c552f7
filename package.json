{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 9003", "dev:turbo": "next dev --turbopack --port 9003", "build": "node --max-old-space-size=4096 ./node_modules/next/dist/bin/next build", "build:static": "node --max-old-space-size=4096 ./node_modules/next/dist/bin/next build", "start": "next start", "serve": "next start", "serve:static": "npx serve out", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@opentelemetry/exporter-jaeger": "^2.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "firebase": "^11.6.1", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "html2canvas": "^1.4.1", "i18next": "^25.2.0", "jspdf": "^3.0.1", "lucide-react": "^0.475.0", "moment": "^2.30.1", "next": "^15.3.1", "next-i18next": "^15.4.2", "patch-package": "^8.0.0", "react": "^18.3.1", "react-big-calendar": "^1.19.2", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "sharp": "^0.34.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.32", "@types/react": "^18", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}