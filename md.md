main-app.js?v=1748621345230:2282 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\config\firebase.ts:28 Firebase initialized using config from firebaseConfig.js
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\config\firebase.ts:45 Firebase Functions configured for production use
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\lib\firebase.ts:28 🔧 Firebase Functions Configuration:
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\lib\firebase.ts:29   - Environment: development
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\lib\firebase.ts:30   - Hostname: *************
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\lib\firebase.ts:31   - Project ID: tasks-intelligence
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\lib\firebase.ts:32   - Region: europe-west1
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\LanguageContext.tsx:108 [LanguageContext] Loaded language from localStorage: ar
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:514 [AuthContext] Displaying global loading spinner. Loading: true User: false Path: /admin/diagnostics
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:348 [AuthContext] Setting up auth state listener.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:355 [AuthContext] Auth state changed, user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Current loading state: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\utils\firestoreListenerManager.ts:47 [FirestoreListenerManager] Removing all 0 listeners
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:373 [AuthContext] 👤 مستخدم مسجل دخول: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:67 [AuthContext] 🔍 جلب بيانات المستخدم: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:116 [AuthContext] ✅ بيانات المستخدم الأساسية: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:121 [AuthContext] 🎯 الخطوة 1: تحديد نوع الحساب
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:122   - userData.accountType: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:123   - نوع الحساب المحدد: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:127 [AuthContext] 👤 الخطوة 2: معالجة حساب فردي
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:147   - userData.role: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:148   - الدور المحدد: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:149   - system_owner: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:150   - system_admin: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:161 [AuthContext] ✅ البيانات النهائية للحساب الفردي: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:378 [AuthContext] ✅ تم تعيين بيانات المستخدم: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:382 [AuthContext] Current path for routing: /admin/diagnostics
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:409 [AuthContext] Finished processing authenticated user. Setting loading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:521 [AuthContext] Rendering children. Loading: false User: true Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:126 [AdminProtectedRoute] Rendering loading spinner (authLoading: false, loading: true)
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:25 [AdminProtectedRoute] Starting check...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:32 [AdminProtectedRoute] Auth loading finished. User: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:44 [AdminProtectedRoute] User found, checking claims...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:53 [AdminProtectedRoute] Forcing token refresh for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:52 [useAccountType] Checking account type from userClaims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:56 [useAccountType] Using userClaims for account type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:458 [AuthContext] Setting up Firestore listener for path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:521 [AuthContext] Rendering children. Loading: false User: true Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:126 [AdminProtectedRoute] Rendering loading spinner (authLoading: false, loading: true)
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:461 [AuthContext] Firestore snapshot for users/aaEo0kh7rHTzgPrQEoYP07VXZdx2 exists: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:56 [AdminProtectedRoute] User Claims: {name: 'trefaeez', role: 'system_owner', accountType: 'individual', system_owner: true, owner: true, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:78 [AdminProtectedRoute] System Owner: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:79 [AdminProtectedRoute] System Admin: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:80 [AdminProtectedRoute] Organization Owner: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:81 [AdminProtectedRoute] Org Admin: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:82 [AdminProtectedRoute] Org Supervisor: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:83 [AdminProtectedRoute] Org Engineer: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:84 [AdminProtectedRoute] Org Technician: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:85 [AdminProtectedRoute] Org Assistant: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:86 [AdminProtectedRoute] Has Owner Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:87 [AdminProtectedRoute] Has Admin Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:100 [AdminProtectedRoute] User IS admin or owner. Access granted.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:114 [AdminProtectedRoute] Check finished.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:136 [AdminProtectedRoute] Rendering children? user: true, isAdmin: true, isOwner: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:411 🚨 [ORGANIZATION REQUESTS DEBUG] Starting check...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:413 🚨 [ORGANIZATION REQUESTS DEBUG] Results: {userClaims: {…}, userClaimsStringified: '{"accountType":"individual","role":"system_owner",…:true,"customPermissions":[],"departmentId":null}', system_owner: true, role: 'system_owner', shouldShow: true, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:424 ✅ [ORGANIZATION REQUESTS] Should show - rendering component
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: loading
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:52 [AccountTypeGuard] Showing loading skeleton
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: loading
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:52 [AccountTypeGuard] Showing loading skeleton
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:52 [useAccountType] Checking account type from userClaims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:56 [useAccountType] Using userClaims for account type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:52 [useAccountType] Checking account type from userClaims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:56 [useAccountType] Using userClaims for account type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useTaskCategories.ts:21 useTaskCategories: useEffect triggered. userId: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useTaskCategories.ts:30 useTaskCategories: Setting up Firestore listener for userId: aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:52 [useAccountType] Checking account type from userClaims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useAccountType.ts:56 [useAccountType] Using userClaims for account type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:411 🚨 [ORGANIZATION REQUESTS DEBUG] Starting check...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:413 🚨 [ORGANIZATION REQUESTS DEBUG] Results: {userClaims: {…}, userClaimsStringified: '{"accountType":"individual","role":"system_owner",…:true,"customPermissions":[],"departmentId":null}', system_owner: true, role: 'system_owner', shouldShow: true, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:424 ✅ [ORGANIZATION REQUESTS] Should show - rendering component
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:125 [usePermissions] useEffect triggered. authContextLoading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:43 [usePermissions] Starting determinePermissions for user: aaEo0kh7rHTzgPrQEoYP07VXZdx2 Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:66 [usePermissions] Effective role from claims: system_owner
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:78 [usePermissions] Fetching custom permissions from path: users/aaEo0kh7rHTzgPrQEoYP07VXZdx2
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
 [usePermissions] Final custom permissions: []
 [usePermissions] Finished determinePermissions, setting internalLoading to false.
 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
 [usePermissions] Final custom permissions: []
 [usePermissions] Finished determinePermissions, setting internalLoading to false.
 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
 [usePermissions] Final custom permissions: []
 [usePermissions] Finished determinePermissions, setting internalLoading to false.
 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
 [usePermissions] Final custom permissions: []
 [usePermissions] Finished determinePermissions, setting internalLoading to false.
 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
 [usePermissions] Final custom permissions: []
 [usePermissions] Finished determinePermissions, setting internalLoading to false.
 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:82 [usePermissions] User document data from Firestore: {departmentId: null, isSystemOwner: true, email: '<EMAIL>', uid: 'aaEo0kh7rHTzgPrQEoYP07VXZdx2', updatedAt: Timestamp, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:85 [usePermissions] Custom permissions set: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:110 [usePermissions] Final custom permissions: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:119 [usePermissions] Finished determinePermissions, setting internalLoading to false.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useTaskCategories.ts:40 useTaskCategories: Firestore snapshot received.
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\useTaskCategories.ts:57 useTaskCategories: Setting categories from Firestore: []
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:411 🚨 [ORGANIZATION REQUESTS DEBUG] Starting check...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:413 🚨 [ORGANIZATION REQUESTS DEBUG] Results: {userClaims: {…}, userClaimsStringified: '{"accountType":"individual","role":"system_owner",…:true,"customPermissions":[],"departmentId":null}', system_owner: true, role: 'system_owner', shouldShow: true, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:424 ✅ [ORGANIZATION REQUESTS] Should show - rendering component
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] المهام: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الخطة اليومية: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الخطة اليومية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] التقارير الأسبوعية: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] التقارير الأسبوعية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] مؤشرات الأداء: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] مؤشرات الأداء: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'tools.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الأدوات: Required permission tools.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الأدوات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'tools.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الاقتراحات الذكية: Required permission tools.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الاقتراحات الذكية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:68 [PermissionSidebarItem] لوحة الإدارة: Required role system_admin, has role: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] لوحة الإدارة: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'users.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] المستخدمين: Required permission users.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] المستخدمين: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'data.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] إدارة البيانات: Required permission data.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] إدارة البيانات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] طلبات المؤسسات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'settings.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الإعدادات: Required permission settings.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الإعدادات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'settings.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الوثائق: Required permission settings.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الوثائق: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\context\AuthContext.tsx:521 [AuthContext] Rendering children. Loading: false User: true Claims: {accountType: 'individual', role: 'system_owner', system_owner: true, system_admin: true, customPermissions: Array(0), …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AdminProtectedRoute.tsx:136 [AdminProtectedRoute] Rendering children? user: true, isAdmin: true, isOwner: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:411 🚨 [ORGANIZATION REQUESTS DEBUG] Starting check...
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:413 🚨 [ORGANIZATION REQUESTS DEBUG] Results: {userClaims: {…}, userClaimsStringified: '{"accountType":"individual","role":"system_owner",…:true,"customPermissions":[],"departmentId":null}', system_owner: true, role: 'system_owner', shouldShow: true, …}
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(app)\AppLayoutContent.tsx:424 ✅ [ORGANIZATION REQUESTS] Should show - rendering component
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] المهام: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الخطة اليومية: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الخطة اليومية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] التقارير الأسبوعية: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] التقارير الأسبوعية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'reports.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] مؤشرات الأداء: Required permission reports.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] مؤشرات الأداء: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'tools.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الأدوات: Required permission tools.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الأدوات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:46 [AccountTypeGuard] Required Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:47 [AccountTypeGuard] Account Type: individual
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:48 [AccountTypeGuard] Is Loading: false
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\auth\AccountTypeGuard.tsx:68 [AccountTypeGuard] Has Access: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'tools.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الاقتراحات الذكية: Required permission tools.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الاقتراحات الذكية: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:68 [PermissionSidebarItem] لوحة الإدارة: Required role system_admin, has role: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] لوحة الإدارة: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'users.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] المستخدمين: Required permission users.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] المستخدمين: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'data.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] إدارة البيانات: Required permission data.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] إدارة البيانات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] طلبات المؤسسات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'settings.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الإعدادات: Required permission settings.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الإعدادات: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\hooks\usePermissions.tsx:162 [usePermissions] hasPermission: User has high-level role 'system_owner', granting access to 'settings.view'
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:76 [PermissionSidebarItem] الوثائق: Required permission settings.view, has permission: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\PermissionSidebarItem.tsx:80 [PermissionSidebarItem] الوثائق: Final access decision: true
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:85 🧪 Testing createUser with data: {email: '<EMAIL>', password: 'testpass123', name: 'Test User', role: 'org_assistant', accountType: 'individual'}
diagnostics:1 Access to fetch at 'https://europe-west1-tasks-intelligence.cloudfunctions.net/createUser' from origin 'http://*************:9003' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.Understand this error
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:88 
            
            
           POST https://europe-west1-tasks-intelligence.cloudfunctions.net/createUser net::ERR_FAILED
fetchImpl @ index.esm2017.js:455
postJSON @ index.esm2017.js:549
callAtURL @ index.esm2017.js:621
await in callAtURL
call @ index.esm2017.js:604
callable @ index.esm2017.js:517
runCreateUserTest @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:88
executeDispatch @ react-dom-client.development.js:16502
runWithFiberInDEV @ react-dom-client.development.js:845
processDispatchQueue @ react-dom-client.development.js:16552
eval @ react-dom-client.development.js:17150
batchedUpdates$1 @ react-dom-client.development.js:3263
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16706
dispatchEvent @ react-dom-client.development.js:20816
dispatchDiscreteEvent @ react-dom-client.development.js:20784
<button>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
_c @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\ui\button.tsx:46
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateForwardRef @ react-dom-client.development.js:8679
beginWork @ react-dom-client.development.js:10895
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<Button>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
CreateUserTest @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:178
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<CreateUserTest>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
DiagnosticsPage @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(admin)\admin\diagnostics\page.tsx:30
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<DiagnosticsPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1031
resolveModel @ react-server-dom-webpack-client.browser.development.js:1599
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2288
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1748621345230:149
options.factory @ webpack.js?v=1748621345230:712
__webpack_require__ @ webpack.js?v=1748621345230:37
fn @ webpack.js?v=1748621345230:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1748621345230:171
options.factory @ webpack.js?v=1748621345230:712
__webpack_require__ @ webpack.js?v=1748621345230:37
__webpack_exec__ @ main-app.js?v=1748621345230:2802
(anonymous) @ main-app.js?v=1748621345230:2803
webpackJsonpCallback @ webpack.js?v=1748621345230:1388
(anonymous) @ main-app.js?v=1748621345230:9Understand this error
C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:112 🚨 Error in createUser test: FirebaseError: internal
error @ intercept-console-error.js:50
runCreateUserTest @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:112
await in runCreateUserTest
executeDispatch @ react-dom-client.development.js:16502
runWithFiberInDEV @ react-dom-client.development.js:845
processDispatchQueue @ react-dom-client.development.js:16552
eval @ react-dom-client.development.js:17150
batchedUpdates$1 @ react-dom-client.development.js:3263
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16706
dispatchEvent @ react-dom-client.development.js:20816
dispatchDiscreteEvent @ react-dom-client.development.js:20784
<button>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
_c @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\ui\button.tsx:46
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateForwardRef @ react-dom-client.development.js:8679
beginWork @ react-dom-client.development.js:10895
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<Button>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
CreateUserTest @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\components\debug\CreateUserTest.tsx:178
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<CreateUserTest>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
DiagnosticsPage @ C:\Users\<USER>\Desktop\Admin\AI-tasks-1\src\app\(admin)\admin\diagnostics\page.tsx:30
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<DiagnosticsPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14526
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1031
resolveModel @ react-server-dom-webpack-client.browser.development.js:1599
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2288
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1748621345230:149
options.factory @ webpack.js?v=1748621345230:712
__webpack_require__ @ webpack.js?v=1748621345230:37
fn @ webpack.js?v=1748621345230:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1748621345230:171
options.factory @ webpack.js?v=1748621345230:712
__webpack_require__ @ webpack.js?v=1748621345230:37
__webpack_exec__ @ main-app.js?v=1748621345230:2802
(anonymous) @ main-app.js?v=1748621345230:2803
webpackJsonpCallback @ webpack.js?v=1748621345230:1388
(anonymous) @ main-app.js?v=1748621345230:9Understand this error