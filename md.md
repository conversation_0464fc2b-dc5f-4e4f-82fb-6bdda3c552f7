starting build "8b2d2b8a-5374-429c-bad9-0732c6404566"
FETCHSOURCE
BUILD
Starting Step #0 - "ubuntu"
Pulling image: ubuntu
Using default tag: latest
latest: Pulling from library/ubuntu
d9d352c11bbd: Pulling fs layer
d9d352c11bbd: Verifying Checksum
d9d352c11bbd: Download complete
d9d352c11bbd: Pull complete
Digest: sha256:b59d21599a2b151e23eea5f6602f4af4d7d31c4e236d22bf0b62b86d2e386b8f
Status: Downloaded newer image for ubuntu:latest
docker.io/library/ubuntu:latest
Finished Step #0 - "ubuntu"
Starting Step #1 - "fetch"
Pulling image: gcr.io/buildpacks/firebase-app-hosting-22/builder:296af0355d8a6dfeb9dd513820312430e29df281
296af0355d8a6dfeb9dd513820312430e29df281: Pulling from buildpacks/firebase-app-hosting-22/builder
7478e0ac0f23: Pulling fs layer
c3dd5f38958c: Pulling fs layer
9709bf2b448f: Pulling fs layer
a74c2b09bb07: Pulling fs layer
999e8b229476: Pulling fs layer
1c9c383d63bc: Pulling fs layer
8f56d1d34e53: Pulling fs layer
182afb1fe5dc: Pulling fs layer
1a82443b3661: Pulling fs layer
2ac72a9c7e8e: Pulling fs layer
1182a6567613: Pulling fs layer
a32a099c9a20: Pulling fs layer
ce58163d65af: Pulling fs layer
ab5065110e9b: Pulling fs layer
52f20bf313bd: Pulling fs layer
42ac8026e9a4: Pulling fs layer
565b08242743: Pulling fs layer
548eb8f86e6e: Pulling fs layer
aba207a2a2a1: Pulling fs layer
2b84881ebdca: Pulling fs layer
4f4fb700ef54: Pulling fs layer
99e0f735f13b: Pulling fs layer
48c38496089a: Pulling fs layer
a74c2b09bb07: Waiting
999e8b229476: Waiting
1c9c383d63bc: Waiting
8f56d1d34e53: Waiting
182afb1fe5dc: Waiting
1a82443b3661: Waiting
2ac72a9c7e8e: Waiting
1182a6567613: Waiting
a32a099c9a20: Waiting
ce58163d65af: Waiting
ab5065110e9b: Waiting
52f20bf313bd: Waiting
42ac8026e9a4: Waiting
565b08242743: Waiting
548eb8f86e6e: Waiting
aba207a2a2a1: Waiting
2b84881ebdca: Waiting
4f4fb700ef54: Waiting
99e0f735f13b: Waiting
48c38496089a: Waiting
c3dd5f38958c: Download complete
7478e0ac0f23: Verifying Checksum
7478e0ac0f23: Download complete
999e8b229476: Download complete
1c9c383d63bc: Verifying Checksum
1c9c383d63bc: Download complete
a74c2b09bb07: Verifying Checksum
a74c2b09bb07: Download complete
8f56d1d34e53: Download complete
182afb1fe5dc: Verifying Checksum
182afb1fe5dc: Download complete
2ac72a9c7e8e: Verifying Checksum
2ac72a9c7e8e: Download complete
1a82443b3661: Verifying Checksum
1a82443b3661: Download complete
1182a6567613: Download complete
a32a099c9a20: Verifying Checksum
a32a099c9a20: Download complete
ce58163d65af: Download complete
ab5065110e9b: Verifying Checksum
ab5065110e9b: Download complete
52f20bf313bd: Verifying Checksum
52f20bf313bd: Download complete
42ac8026e9a4: Verifying Checksum
42ac8026e9a4: Download complete
548eb8f86e6e: Verifying Checksum
548eb8f86e6e: Download complete
565b08242743: Verifying Checksum
565b08242743: Download complete
aba207a2a2a1: Verifying Checksum
aba207a2a2a1: Download complete
2b84881ebdca: Verifying Checksum
2b84881ebdca: Download complete
4f4fb700ef54: Verifying Checksum
4f4fb700ef54: Download complete
9709bf2b448f: Verifying Checksum
9709bf2b448f: Download complete
99e0f735f13b: Verifying Checksum
99e0f735f13b: Download complete
48c38496089a: Verifying Checksum
48c38496089a: Download complete
7478e0ac0f23: Pull complete
c3dd5f38958c: Pull complete
9709bf2b448f: Pull complete
a74c2b09bb07: Pull complete
999e8b229476: Pull complete
1c9c383d63bc: Pull complete
8f56d1d34e53: Pull complete
182afb1fe5dc: Pull complete
1a82443b3661: Pull complete
2ac72a9c7e8e: Pull complete
1182a6567613: Pull complete
a32a099c9a20: Pull complete
ce58163d65af: Pull complete
ab5065110e9b: Pull complete
52f20bf313bd: Pull complete
42ac8026e9a4: Pull complete
565b08242743: Pull complete
548eb8f86e6e: Pull complete
aba207a2a2a1: Pull complete
2b84881ebdca: Pull complete
4f4fb700ef54: Pull complete
99e0f735f13b: Pull complete
48c38496089a: Pull complete
Digest: sha256:b9f2101b272793a52d47e2175da58c9d939b4e10399912af06789e036e246acf
Status: Downloaded newer image for gcr.io/buildpacks/firebase-app-hosting-22/builder:296af0355d8a6dfeb9dd513820312430e29df281
gcr.io/buildpacks/firebase-app-hosting-22/builder:296af0355d8a6dfeb9dd513820312430e29df281
=== Fetch Source from Signed URL ===
Downloading source code from signed URL...
Extracting archive...
Moving source code into workspace...
Cleaning up temporary files and directories...
Downloaded and unpacked source code from signed URL
Finished Step #1 - "fetch"
Starting Step #2 - "preparer"
Already have image (with digest): gcr.io/buildpacks/firebase-app-hosting-22/builder:296af0355d8a6dfeb9dd513820312430e29df281
2025/06/10 22:44:48 FIREBASE_CONFIG has no availability specified, applying the default of 'BUILD' and 'RUNTIME'
2025/06/10 22:44:48 Final app hosting schema:
runConfig:
  cpu: 1
  memoryMiB: 512
  concurrency: 80
  maxInstances: 8
  minInstances: 0
  vpcAccess: null
env:
- variable: FIREBASE_CONFIG
  value: '{"databaseURL":"","projectId":"tasks-intelligence","storageBucket":"tasks-intelligence.firebasestorage.app"}'
  availability:
  - BUILD
  - RUNTIME
- variable: FIREBASE_WEBAPP_CONFIG
  value: '{"apiKey":"AIzaSyBIJrQX5HBBnP7LKzgsUNdWCX7aqhVG3wA","appId":"1:770714758504:web:aea98ba39a726df1ba3add","authDomain":"tasks-intelligence.firebaseapp.com","databaseURL":"","messagingSenderId":"770714758504","projectId":"tasks-intelligence","storageBucket":"tasks-intelligence.firebasestorage.app"}'
  availability:
  - BUILD
2025/06/10 22:44:48 Final app hosting schema:
runConfig:
  cpu: 1
  memoryMiB: 512
  concurrency: 80
  maxInstances: 8
  minInstances: 0
  vpcAccess: null
env:
- variable: FIREBASE_CONFIG
  value: '{"databaseURL":"","projectId":"tasks-intelligence","storageBucket":"tasks-intelligence.firebasestorage.app"}'
  availability:
  - BUILD
  - RUNTIME
- variable: FIREBASE_WEBAPP_CONFIG
  value: '{"apiKey":"AIzaSyBIJrQX5HBBnP7LKzgsUNdWCX7aqhVG3wA","appId":"1:770714758504:web:aea98ba39a726df1ba3add","authDomain":"tasks-intelligence.firebaseapp.com","databaseURL":"","messagingSenderId":"770714758504","projectId":"tasks-intelligence","storageBucket":"tasks-intelligence.firebasestorage.app"}'
  availability:
  - BUILD
Finished Step #2 - "preparer"
Starting Step #3 - "pack"
Pulling image: gcr.io/k8s-skaffold/pack
Using default tag: latest
latest: Pulling from k8s-skaffold/pack
396c31837116: Pulling fs layer
9776b10d5c8c: Pulling fs layer
52cb9ac3197f: Pulling fs layer
9776b10d5c8c: Verifying Checksum
9776b10d5c8c: Download complete
396c31837116: Verifying Checksum
396c31837116: Download complete
52cb9ac3197f: Verifying Checksum
52cb9ac3197f: Download complete
396c31837116: Pull complete
9776b10d5c8c: Pull complete
52cb9ac3197f: Pull complete
Digest: sha256:221c0c0d9a90f46f108bb888a1da9e99c82ff631e8b1c63b0223ea951752bd53
Status: Downloaded newer image for gcr.io/k8s-skaffold/pack:latest
gcr.io/k8s-skaffold/pack:latest
296af0355d8a6dfeb9dd513820312430e29df281: Pulling from buildpacks/firebase-app-hosting-22/builder
Digest: sha256:b9f2101b272793a52d47e2175da58c9d939b4e10399912af06789e036e246acf
Status: Image is up to date for gcr.io/buildpacks/firebase-app-hosting-22/builder:296af0355d8a6dfeb9dd513820312430e29df281
latest: Pulling from buildpacks/firebase-app-hosting-22/run
e1a89dea01a6: Already exists
af059eb152dc: Pulling fs layer
66db767fbc86: Pulling fs layer
4972b34f89ae: Pulling fs layer
9871e18911f2: Pulling fs layer
9871e18911f2: Waiting
af059eb152dc: Verifying Checksum
af059eb152dc: Download complete
9871e18911f2: Verifying Checksum
9871e18911f2: Download complete
af059eb152dc: Pull complete
66db767fbc86: Verifying Checksum
66db767fbc86: Download complete
4972b34f89ae: Verifying Checksum
4972b34f89ae: Download complete
66db767fbc86: Pull complete
4972b34f89ae: Pull complete
9871e18911f2: Pull complete
Digest: sha256:b89ca4ca12099fe76da6e0f6a30430f2b21557ee8ae07b61b9963143c4862dff
Status: Downloaded newer image for gcr.io/buildpacks/firebase-app-hosting-22/run:latest
===> ANALYZING
Image with name "us-central1-docker.pkg.dev/tasks-intelligence/firebaseapphosting-images/studio:a-zubeo5sapt" not found
===> DETECTING
target distro name/version labels not found, reading /etc/os-release file
4 of 5 buildpacks participating
google.nodejs.runtime        1.0.0
google.nodejs.firebasenextjs 0.0.1
google.nodejs.npm            1.1.0
google.nodejs.firebasebundle 0.0.1
===> RESTORING
===> BUILDING
target distro name/version labels not found, reading /etc/os-release file
=== Node.js - Runtime (google.nodejs.runtime@1.0.0) ===
2025/06/10 22:45:05 [DEBUG] GET https://dl.google.com/runtimes/ubuntu2204/nodejs/version.json
Adding image label google.runtime-version: nodejs20
2025/06/10 22:45:06 [DEBUG] GET https://dl.google.com/runtimes/ubuntu2204/nodejs/version.json
***** CACHE MISS: "nodejs"
Installing Node.js v20.19.2.
2025/06/10 22:45:06 [DEBUG] GET https://dl.google.com/runtimes/ubuntu2204/nodejs/nodejs-20.19.2.tar.gz
=== Node.js - Firebasenextjs (google.nodejs.firebasenextjs@0.0.1) ===
WARNING: *** You are using a custom build command (your build command is NOT 'next build'), we will accept it as is but will error if output structure is not as expected ***
***** CACHE MISS: "npm_modules"
Installing nextjs adaptor 14.0.12
=== Node.js - Npm (google.nodejs.npm@1.1.0) ===
***** CACHE MISS: "npm_modules"
Installing application dependencies.
--------------------------------------------------------------------------------
Running "npm ci --quiet --no-fund --no-audit (NODE_ENV=development)"
npm warn deprecated rimraf@2.7.1: Rimraf versions prior to v4 are no longer supported
npm warn deprecated lodash.isequal@4.5.0: This package is deprecated. Use require('node:util').isDeepStrictEqual instead.
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated fstream@1.0.12: This package is no longer supported.
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
added 847 packages in 50s
Done "npm ci --quiet --no-fund --no-audit (NODE_ENV=development)" (50.661606424s)
--------------------------------------------------------------------------------
Running "npm exec --prefix /layers/google.nodejs.firebasenextjs/npm_modules apphosting-adapter-nextjs-build"
Overriding Next Config to add configs optmized for Firebase App Hosting
Successfully created next.config.js with Firebase App Hosting overrides
> nextn@0.1.0 build
> node --max-old-space-size=4096 ./node_modules/next/dist/bin/next build
   ▲ Next.js 15.3.1
   - Environments: .env.local, .env.production
   Creating an optimized production build ...
Failed to compile.
./src/components/WeeklyReportCard.tsx
Error:   x Return statement is not allowed here
     ,-[/workspace/src/components/WeeklyReportCard.tsx:594:1]
 591 |       };
 592 |     
 593 |       if (isLoadingTasks) {
 594 | ,->     return (
 595 | |         <Card className={cn("shadow-md", className)}>
 596 | |           <CardContent className="space-y-4 pt-6">
 597 | |             <Skeleton className="h-8 w-full" />
 598 | |             <Skeleton className="h-24 w-full" />
 599 | |             <Skeleton className="h-8 w-3/4" />
 600 | |             <Skeleton className="h-8 w-1/2" />
 601 | |           </CardContent>
 602 | |         </Card>
 603 | `->     );
 604 |       }
 605 |     
 606 |       if (error) {
     `----
  x Return statement is not allowed here
     ,-[/workspace/src/components/WeeklyReportCard.tsx:607:1]
 604 |       }
 605 |     
 606 |       if (error) {
 607 | ,->     return (
 608 | |         <Card className={cn("shadow-md", className)}>
 609 | |           <CardContent className="pt-6">
 610 | |             <Alert variant="destructive">
 611 | |               <AlertTriangle className="h-4 w-4" />
 612 | |               <AlertTitle>خطأ</AlertTitle>
 613 | |               <AlertDescription>{error}</AlertDescription>
 614 | |             </Alert>
 615 | |           </CardContent>
 616 | |         </Card>
 617 | `->     );
 618 |       }
 619 |     
 620 |       return (
     `----
  x Return statement is not allowed here
      ,-[/workspace/src/components/WeeklyReportCard.tsx:620:1]
  617 |         );
  618 |       }
  619 |     
  620 | ,->   return (
  621 | |       <Card className={cn("shadow-md", className)} ref={reportElementRef}>
  622 | |         <CardContent className="space-y-6 pt-6">
  623 | |           {!report ? (
  624 | |             <div className="text-center py-8">
  625 | |               <Button
  626 | |                 onClick={() => handleGenerateReport(false)}
  627 | |                 disabled={isGeneratingReport || tasks.length === 0}
  628 | |                 className="bg-primary hover:bg-primary/90 text-primary-foreground"
  629 | |               >
  630 | |                 {isGeneratingReport ? (
  631 | |                   <>
  632 | |                     <Loader2 className="ml-2 h-4 w-4 animate-spin" />
  633 | |                     جاري إنشاء التقرير...
  634 | |                   </>
  635 | |                 ) : (
  636 | |                   <>
  637 | |                     <FileText className="ml-2 h-4 w-4" />
  638 | |                     إنشاء التقرير الأسبوعي
  639 | |                   </>
  640 | |                 )}
  641 | |               </Button>
  642 | |               {tasks.length === 0 && (
  643 | |                 <p className="text-sm text-muted-foreground mt-4">
  644 | |                   لا توجد مهام متاحة لإنشاء التقرير.
  645 | |                 </p>
  646 | |               )}
  647 | |             </div>
  648 | |           ) : (
  649 | |             <>
  650 | |               <div className="flex justify-between items-center">
  651 | |                 <h2 className="text-xl font-bold">
  652 | |                   {report.title || `التقرير الأسبوعي للفترة من ${formatDate(reportPeriod.startDate)} إلى ${formatDate(reportPeriod.endDate)}`}
  653 | |                 </h2>
  654 | |                 <div className="flex gap-2">
  655 | |                   <Button variant="outline" size="sm" onClick={handleExportPDF}>
  656 | |                     <Download className="ml-1 h-4 w-4" />
  657 | |                     تصدير
  658 | |                   </Button>
  659 | |                   <Button variant="outline" size="sm" onClick={handlePrint}>
  660 | |                     <Printer className="ml-1 h-4 w-4" />
  661 | |                     طباعة
  662 | |                   </Button>
  663 | |                   <Button variant="outline" size="sm" onClick={handleShare}>
  664 | |                     <Share2 className="ml-1 h-4 w-4" />
  665 | |                     مشاركة
  666 | |                   </Button>
  667 | |                 </div>
  668 | |               </div>
  669 | |   
  670 | |               <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
  671 | |                 <TabsList className="grid grid-cols-4 lg:grid-cols-10 mb-4">
  672 | |                   <TabsTrigger value="summary">الملخص</TabsTrigger>
  673 | |                   <TabsTrigger value="analysis">
  674 | |                     <BarChart className="ml-1 h-4 w-4" />
  675 | |                     التحليل
  676 | |                   </TabsTrigger>
  677 | |                   <TabsTrigger value="planning">
  678 | |                     <ListChecks className="ml-1 h-4 w-4" />
  679 | |                     التخطيط
  680 | |                   </TabsTrigger>
  681 | |                   <TabsTrigger value="completed">
  682 | |                     مكتملة ({report?.completedTasks?.length || 0})
  683 | |                   </TabsTrigger>
  684 | |                   <TabsTrigger value="inProgress">
  685 | |                     قيد الانتظار ({report?.inProgressTasks?.length || 0})
  686 | |                   </TabsTrigger>
  687 | |                   <TabsTrigger value="upcoming">
  688 | |                     قادمة ({report?.upcomingTasks?.length || 0})
  689 | |                   </TabsTrigger>
  690 | |                   <TabsTrigger value="overdue">
  691 | |                     فائتة ({report?.overdueTasks?.length || 0})
  692 | |                   </TabsTrigger>
  693 | |                   <TabsTrigger value="charts">
  694 | |                     <BarChart className="ml-1 h-4 w-4" />
  695 | |                     الرسوم البيانية
  696 | |                   </TabsTrigger>
  697 | |                   <TabsTrigger value="trends">
  698 | |                     <TrendingUp className="ml-1 h-4 w-4" />
  699 | |                     الاتجاهات
  700 | |                   </TabsTrigger>
  701 | |                   <TabsTrigger value="departments">
  702 | |                     <Building className="ml-1 h-4 w-4" />
  703 | |                     الأقسام
  704 | |                   </TabsTrigger>
  705 | |                   <TabsTrigger value="export">
  706 | |                     <Download className="ml-1 h-4 w-4" />
  707 | |                     تصدير
  708 | |                   </TabsTrigger>
  709 | |                 </TabsList>
  710 | |   
  711 | |                 <TabsContent value="analysis" className="space-y-4">
  712 | |                   <div className="bg-muted/30 p-4 rounded-lg">
  713 | |                     <h3 className="text-lg font-semibold mb-3">تحليل الأداء الأسبوعي</h3>
  714 | |                     <p className="text-right leading-7 mb-4">
  715 | |                       يقدم هذا التحليل نظرة متعمقة على أدائك خلال الأسبوع، مع تسليط الضوء على الاتجاهات والأنماط التي يمكن أن تساعدك في تحسين إنتاجيتك.
  716 | |                     </p>
  717 | |                   </div>
  718 | |   
  719 | |                   {/* تحليل توزيع المهام حسب الحالة */}
  720 | |                   <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
  721 | |                     <Card>
  722 | |                       <CardHeader className="pb-2">
  723 | |                         <CardTitle className="text-base">توزيع المهام حسب الحالة</CardTitle>
  724 | |                       </CardHeader>
  725 | |                       <CardContent>
  726 | |                         <div className="space-y-2">
  727 | |                           {/* المهام المكتملة */}
  728 | |                           <div className="space-y-1">
  729 | |                             <div className="flex justify-between text-sm">
  730 | |                               <span className="text-muted-foreground">المكتملة</span>
  731 | |                               <span className="font-medium">{report?.completedTasks?.length || 0} ({Math.round(((report?.completedTasks?.length || 0) / (
  732 | |                                 (report?.completedTasks?.length || 0) +
  733 | |                                 (report?.inProgressTasks?.length || 0) +
  734 | |                                 (report?.upcomingTasks?.length || 0) +
  735 | |                                 (report?.blockedTasks?.length || 0)
  736 | |                               ) * 100) || 0)}%!)(MISSING)</span>
  737 | |                             </div>
  738 | |                             <Progress value={Math.round(((report?.completedTasks?.length || 0) / (
  739 | |                               (report?.completedTasks?.length || 0) +
  740 | |                               (report?.inProgressTasks?.length || 0) +
  741 | |                               (report?.upcomingTasks?.length || 0) +
  742 | |                               (report?.blockedTasks?.length || 0)
  743 | |                             ) * 100) || 0)} className="h-2 bg-muted" />
  744 | |                           </div>
  745 | |                           {/* المهام قيد التنفيذ */}
  746 | |                           <div className="space-y-1">
  747 | |                             <div className="flex justify-between text-sm">
  748 | |                               <span className="text-muted-foreground">قيد التنفيذ</span>
  749 | |                               <span className="font-medium">{report?.inProgressTasks?.length || 0} ({Math.round(((report?.inProgressTasks?.length || 0) / (
  750 | |                                 (report?.completedTasks?.length || 0) +
  751 | |                                 (report?.inProgressTasks?.length || 0) +
  752 | |                                 (report?.upcomingTasks?.length || 0) +
  753 | |                                 (report?.blockedTasks?.length || 0)
  754 | |                               ) * 100) || 0)}%!)(MISSING)</span>
  755 | |                             </div>
  756 | |                             <Progress value={Math.round(((report?.inProgressTasks?.length || 0) / (
  757 | |                               (report?.completedTasks?.length || 0) +
  758 | |                               (report?.inProgressTasks?.length || 0) +
  759 | |                               (report?.upcomingTasks?.length || 0) +
  760 | |                               (report?.blockedTasks?.length || 0)
  761 | |                             ) * 100) || 0)} className="h-2 bg-blue-100" />
  762 | |                           </div>
  763 | |                           {/* المهام القادمة */}
  764 | |                           <div className="space-y-1">
  765 | |                             <div className="flex justify-between text-sm">
  766 | |                               <span className="text-muted-foreground">القادمة</span>
  767 | |                               <span className="font-medium">{report?.upcomingTasks?.length || 0} ({Math.round(((report?.upcomingTasks?.length || 0) / (
  768 | |                                 (report?.completedTasks?.length || 0) +
  769 | |                                 (report?.inProgressTasks?.length || 0) +
  770 | |                                 (report?.upcomingTasks?.length || 0) +
  771 | |                                 (report?.blockedTasks?.length || 0)
  772 | |                               ) * 100) || 0)}%!)(MISSING)</span>
  773 | |                             </div>
  774 | |                             <Progress value={Math.round(((report?.upcomingTasks?.length || 0) / (
  775 | |                               (report?.completedTasks?.length || 0) +
  776 | |                               (report?.inProgressTasks?.length || 0) +
  777 | |                               (report?.upcomingTasks?.length || 0) +
  778 | |                               (report?.blockedTasks?.length || 0)
  779 | |                             ) * 100) || 0)} className="h-2 bg-orange-100" />
  780 | |                           </div>
  781 | |                           {/* المهام المعلقة */}
  782 | |                           <div className="space-y-1">
  783 | |                             <div className="flex justify-between text-sm">
  784 | |                               <span className="text-muted-foreground">المعلقة</span>
  785 | |                               <span className="font-medium">{report?.blockedTasks?.length || 0} ({Math.round(((report?.blockedTasks?.length || 0) / (
  786 | |                                 (report?.completedTasks?.length || 0) +
  787 | |                                 (report?.inProgressTasks?.length || 0) +
  788 | |                                 (report?.upcomingTasks?.length || 0) +
  789 | |                                 (report?.blockedTasks?.length || 0)
  790 | |                               ) * 100) || 0)}%!)(MISSING)</span>
  791 | |                             </div>
  792 | |                             <Progress value={Math.round(((report?.blockedTasks?.length || 0) / (
  793 | |                               (report?.completedTasks?.length || 0) +
  794 | |                               (report?.inProgressTasks?.length || 0) +
  795 | |                               (report?.upcomingTasks?.length || 0) +
  796 | |                               (report?.blockedTasks?.length || 0)
  797 | |                             ) * 100) || 0)} className="h-2 bg-gray-100" />
  798 | |                           </div>
  799 | |                         </div>
  800 | |                       </CardContent>
  801 | |                     </Card>
  802 | |   
  803 | |                     {/* تحليل توزيع المهام حسب الأولوية */}
  804 | |                     <Card>
  805 | |                       <CardHeader className="pb-2">
  806 | |                         <CardTitle className="text-base">توزيع المهام حسب الأولوية</CardTitle>
  807 | |                       </CardHeader>
  808 | |                       <CardContent>
  809 | |                         <div className="space-y-2">
  810 | |                           {/* أولوية عالية */}
  811 | |                           <div className="space-y-1">
  812 | |                             <div className="flex justify-between text-sm">
  813 | |                               <span className="text-muted-foreground">أولوية عالية</span>
  814 | |                               <span className="font-medium">{
  815 | |                                 (report?.completedTasks?.filter(t => t.priority === 'high').length || 0) +
  816 | |                                 (report?.inProgressTasks?.filter(t => t.priority === 'high').length || 0) +
  817 | |                                 (report?.upcomingTasks?.filter(t => t.priority === 'high').length || 0) +
  818 | |                                 (report?.blockedTasks?.filter(t => t.priority === 'high').length || 0)
  819 | |                               }</span>
  820 | |                             </div>
  821 | |                             <Progress value={Math.round((
  822 | |                               ((report?.completedTasks?.filter(t => t.priority === 'high').length || 0) +
  823 | |                               (report?.inProgressTasks?.filter(t => t.priority === 'high').length || 0) +
  824 | |                               (report?.upcomingTasks?.filter(t => t.priority === 'high').length || 0) +
  825 | |                               (report?.blockedTasks?.filter(t => t.priority === 'high').length || 0)) / (
  826 | |                                 (report?.completedTasks?.length || 0) +
  827 | |                                 (report?.inProgressTasks?.length || 0) +
  828 | |                                 (report?.upcomingTasks?.length || 0) +
  829 | |                                 (report?.blockedTasks?.length || 0)
  830 | |                               ) * 100) || 0)} className="h-2 bg-red-100" />
  831 | |                           </div>
  832 | |                           {/* أولوية متوسطة */}
  833 | |                           <div className="space-y-1">
  834 | |                             <div className="flex justify-between text-sm">
  835 | |                               <span className="text-muted-foreground">أولوية متوسطة</span>
  836 | |                               <span className="font-medium">{
  837 | |                                 (report?.completedTasks?.filter(t => t.priority === 'medium').length || 0) +
  838 | |                                 (report?.inProgressTasks?.filter(t => t.priority === 'medium').length || 0) +
  839 | |                                 (report?.upcomingTasks?.filter(t => t.priority === 'medium').length || 0) +
  840 | |                                 (report?.blockedTasks?.filter(t => t.priority === 'medium').length || 0)
  841 | |                               }</span>
  842 | |                             </div>
  843 | |                             <Progress value={Math.round((
  844 | |                               ((report?.completedTasks?.filter(t => t.priority === 'medium').length || 0) +
  845 | |                               (report?.inProgressTasks?.filter(t => t.priority === 'medium').length || 0) +
  846 | |                               (report?.upcomingTasks?.filter(t => t.priority === 'medium').length || 0) +
  847 | |                               (report?.blockedTasks?.filter(t => t.priority === 'medium').length || 0)) / (
  848 | |                                 (report?.completedTasks?.length || 0) +
  849 | |                                 (report?.inProgressTasks?.length || 0) +
  850 | |                                 (report?.upcomingTasks?.length || 0) +
  851 | |                                 (report?.blockedTasks?.length || 0)
  852 | |                               ) * 100) || 0)} className="h-2 bg-yellow-100" />
  853 | |                           </div>
  854 | |                           {/* أولوية منخفضة */}
  855 | |                           <div className="space-y-1">
  856 | |                             <div className="flex justify-between text-sm">
  857 | |                               <span className="text-muted-foreground">أولوية منخفضة</span>
  858 | |                               <span className="font-medium">{
  859 | |                                 (report?.completedTasks?.filter(t => t.priority === 'low').length || 0) +
  860 | |                                 (report?.inProgressTasks?.filter(t => t.priority === 'low').length || 0) +
  861 | |                                 (report?.upcomingTasks?.filter(t => t.priority === 'low').length || 0) +
  862 | |                                 (report?.blockedTasks?.filter(t => t.priority === 'low').length || 0)
  863 | |                               }</span>
  864 | |                             </div>
  865 | |                             <Progress value={Math.round((
  866 | |                               ((report?.completedTasks?.filter(t => t.priority === 'low').length || 0) +
  867 | |                               (report?.inProgressTasks?.filter(t => t.priority === 'low').length || 0) +
  868 | |                               (report?.upcomingTasks?.filter(t => t.priority === 'low').length || 0) +
  869 | |                               (report?.blockedTasks?.filter(t => t.priority === 'low').length || 0)) / (
  870 | |                                 (report?.completedTasks?.length || 0) +
  871 | |                                 (report?.inProgressTasks?.length || 0) +
  872 | |                                 (report?.upcomingTasks?.length || 0) +
  873 | |                                 (report?.blockedTasks?.length || 0)
  874 | |                               ) * 100) || 0)} className="h-2 bg-green-100" />
  875 | |                           </div>
  876 | |                           {/* بدون أولوية */}
  877 | |                           <div className="space-y-1">
  878 | |                             <div className="flex justify-between text-sm">
  879 | |                               <span className="text-muted-foreground">بدون أولوية</span>
  880 | |                               <span className="font-medium">{
  881 | |                                 (report?.completedTasks?.filter(t => !t.priority).length || 0) +
  882 | |                                 (report?.inProgressTasks?.filter(t => !t.priority).length || 0) +
  883 | |                                 (report?.upcomingTasks?.filter(t => !t.priority).length || 0) +
  884 | |                                 (report?.blockedTasks?.filter(t => !t.priority).length || 0)
  885 | |                               }</span>
  886 | |                             </div>
  887 | |                             <Progress value={Math.round((
  888 | |                               ((report?.completedTasks?.filter(t => !t.priority).length || 0) +
  889 | |                               (report?.inProgressTasks?.filter(t => !t.priority).length || 0) +
  890 | |                               (report?.upcomingTasks?.filter(t => !t.priority).length || 0) +
  891 | |                               (report?.blockedTasks?.filter(t => !t.priority).length || 0)) / (
  892 | |                                 (report?.completedTasks?.length || 0) +
  893 | |                                 (report?.inProgressTasks?.length || 0) +
  894 | |                                 (report?.upcomingTasks?.length || 0) +
  895 | |                                 (report?.blockedTasks?.length || 0)
  896 | |                               ) * 100) || 0)} className="h-2 bg-gray-100" />
  897 | |                           </div>
  898 | |                         </div>
  899 | |                       </CardContent>
  900 | |                     </Card>
  901 | |                   </div>
  902 | |   
  903 | |                   {/* تحليل الاتجاهات والأنماط */}
  904 | |                   <Card className="mt-4">
  905 | |                     <CardHeader>
  906 | |                       <CardTitle>تحليل الاتجاهات والأنماط</CardTitle>
  907 | |                       <CardDescription>
  908 | |                         تحليل لأنماط العمل والإنتاجية خلال الأسبوع
  909 | |                       </CardDescription>
  910 | |                     </CardHeader>
  911 | |                     <CardContent>
  912 | |                       <div className="space-y-4">
  913 | |                         {/* نقاط القوة */}
  914 | |                         <div>
  915 | |                           <h4 className="text-sm font-semibold mb-2 flex items-center">
  916 | |                             <span className="inline-block w-3 h-3 rounded-full bg-green-500 ml-2"></span>
  917 | |                             نقاط القوة
  918 | |                           </h4>
  919 | |                           <ul className="space-y-1 text-sm pr-5">
  920 | |                             {report?.completedTasks?.length ? (
  921 | |                               <li className="list-disc">
  922 | |                                 أكملت {report.completedTasks.length} مهام خلال هذا الأسبوع، مما يدل على مستوى جيد من الإنتاجية.
  923 | |                               </li>
  924 | |                             ) : null}
  925 | |                             {report?.keyMetrics?.onTimeCompletionRate && report.keyMetrics.onTimeCompletionRate > 70 ? (
  926 | |                               <li className="list-disc">
  927 | |                                 معدل الإكمال في الوقت المحدد ({report.keyMetrics.onTimeCompletionRate}%!)(MISSING) مرتفع، مما يدل على إدارة جيدة للوقت.
  928 | |                               </li>
  929 | |                             ) : null}
  930 | |                             {report?.inProgressTasks?.length ? (
  931 | |                               <li className="list-disc">
  932 | |                                 لديك {report.inProgressTasks.length} مهام قيد التنفيذ، مما يدل على استمرارية العمل.
  933 | |                               </li>
  934 | |                             ) : null}
  935 | |                             {!report?.blockedTasks?.length ? (
  936 | |                               <li className="list-disc">
  937 | |                                 لا توجد مهام معلقة، مما يدل على قدرتك على تجاوز العقبات بفعالية.
  938 | |                               </li>
  939 | |                             ) : null}
  940 | |                           </ul>
  941 | |                         </div>
  942 | |   
  943 | |                         {/* مجالات التحسين */}
  944 | |                         <div>
  945 | |                           <h4 className="text-sm font-semibold mb-2 flex items-center">
  946 | |                             <span className="inline-block w-3 h-3 rounded-full bg-amber-500 ml-2"></span>
  947 | |                             مجالات التحسين
  948 | |                           </h4>
  949 | |                           <ul className="space-y-1 text-sm pr-5">
  950 | |                             {report?.keyMetrics?.completion
Rate && report.keyMetrics.completionRate < 50 ? (
  951 | |                               <li className="list-disc">
  952 | |                                 معدل إكمال المهام ({report.keyMetrics.completionRate}%!)(MISSING) منخفض، يمكن تحسينه من خلال تقسيم المهام الكبيرة إلى مهام أصغر.
  953 | |                               </li>
  954 | |                             ) : null}
  955 | |                             {report?.keyMetrics?.onTimeCompletionRate && report.keyMetrics.onTimeCompletionRate < 50 ? (
  956 | |                               <li className="list-disc">
  957 | |                                 معدل الإكمال في الوقت المحدد ({report.keyMetrics.onTimeCompletionRate}%!)(MISSING) منخفض، يمكن تحسينه من خلال تحديد مواعيد نهائية أكثر واقعية.
  958 | |                               </li>
  959 | |                             ) : null}
  960 | |                             {report?.blockedTasks?.length ? (
  961 | |                               <li className="list-disc">
  962 | |                                 لديك {report.blockedTasks.length} مهام معلقة، يجب التركيز على حل المشاكل التي تعيقها.
  963 | |                               </li>
  964 | |                             ) : null}
  965 | |                             {report?.inProgressTasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length ? (
  966 | |                               <li className="list-disc">
  967 | |                                 لديك {report.inProgressTasks.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length} مهام متأخرة قيد التنفيذ، يجب إعطاؤها الأولوية.
  968 | |                               </li>
  969 | |                             ) : null}
  970 | |                           </ul>
  971 | |                         </div>
  972 | |   
  973 | |                         {/* الاتجاهات */}
  974 | |                         <div>
  975 | |                           <h4 className="text-sm font-semibold mb-2 flex items-center">
  976 | |                             <span className="inline-block w-3 h-3 rounded-full bg-blue-500 ml-2"></span>
  977 | |                             الاتجاهات الملحوظة
  978 | |                           </h4>
  979 | |                           <ul className="space-y-1 text-sm pr-5">
  980 | |                             {report?.completedTasks?.length && report?.inProgressTasks?.length ? (
  981 | |                               <li className="list-disc">
  982 | |                                 نسبة المهام المكتملة إلى المهام قيد التنفيذ هي {Math.round((report.completedTasks.length / report.inProgressTasks.length) * 100) / 100}،
  983 | |                                 {report.completedTasks.length > report.inProgressTasks.length ? ' مما يدل على تركيز جيد على إكمال المهام.' : ' مما يشير إلى الحاجة للتركيز أكثر 
على إكمال المهام الحالية قبل البدء بمهام جديدة.'}
  984 | |                               </li>
  985 | |                             ) : null}
  986 | |                             {report?.completedTasks?.filter(t => t.priority === 'high').length ? (
  987 | |                               <li className="list-disc">
  988 | |                                 أكملت {report.completedTasks.filter(t => t.priority === 'high').length} مهام ذات أولوية عالية،
  989 | |                                 {report.completedTasks.filter(t => t.priority === 'high').length > (report.completedTasks.filter(t => t.priority === 'medium').length || 0) ? ' مما يدل على تركيز جيد على المهام المهمة.' : ' ولكن يجب التركيز أكثر على المهام ذات الأولوية العالية.'}
  990 | |                               </li>
  991 | |                             ) : null}
  992 | |                             {report?.upcomingTasks?.length ? (
  993 | |                               <li className="list-disc">
  994 | |                                 لديك {report.upcomingTasks.length} مهام قادمة،
  995 | |                                 {report.upcomingTasks.length > 5 ? ' مما قد يشير إلى ضغط عمل كبير في المستقبل القريب. يُنصح بالتخطيط المسبق.' : ' وهو عدد معقول يمكن إدارته بفعالية.'}
  996 | |                               </li>
  997 | |                             ) : null}
  998 | |                           </ul>
  999 | |                         </div>
 1000 | |                       </div>
 1001 | |                     </CardContent>
 1002 | |                   </Card>
 1003 | |                 </TabsContent>
 1004 | |   
 1005 | |                 <TabsContent value="summary" className="space-y-4">
 1006 | |                   <div className="bg-muted/30 p-4 rounded-lg">
 1007 | |                     <p className="text-right leading-7 mb-2">
 1008 | |                       {report?.summary ? (
 1009 | |                         // تنسيق الملخص المستلم من الخدمة
 1010 | |                         report.summary.split('. ').map((sentence, index) => (
 1011 | |                           <span key={index} className="block mb-1">{sentence.trim()}{sentence.trim().length > 0 ? '.' : ''}</span>
 1012 | |                         ))
 1013 | |                       ) : (
 1014 | |                         // إنشاء ملخص افتراضي منسق
 1015 | |                         <>
 1016 | |                           <span className="block mb-2">
 1017 | |                             خلال الفترة من {formatDate(reportPeriod.startDate)} إلى {formatDate(reportPeriod.endDate)}، تم إكمال {report?.completedTasks?.length || 0} مهام من أصل {
 1018 | |                               (report?.completedTasks?.length || 0) +
 1019 | |                               (report?.inProgressTasks?.length || 0) +
 1020 | |                               (report?.upcomingTasks?.length || 0) +
 1021 | |                               (report?.blockedTasks?.length || 0)
 1022 | |                             } مهام.
 1023 | |                           </span>
 1024 | |                           <span className="block mb-2">
 1025 | |                             معدل إكمال المهام: {Math.max(1, calculateCompletionRate(report))}%!
(MISSING) 1026 | |                           </span>
 1027 | |   
 1028 | |                           {report?.blockedTasks?.length ? <span className="block mb-1">هناك {report.blockedTasks.length} مهام معلقة تحتاج إلى متابعة.</span> : ''}
 1029 | |                           {report?.inProgressTasks?.length ? <span className="block mb-1">هناك {report.inProgressTasks.length} مهام قيد التنفيذ.</span> : ''}
 1030 | |                           {report?.upcomingTasks?.length ? <span className="block mb-1">هناك {report.upcomingTasks.length} مهام قادمة.</span> : ''}
 1031 | |   
 1032 | |                           <span className="block mt-2">
 1033 | |                             يجب التركيز على إكمال المهام المتأخرة وتحسين إدارة الوقت.
 1034 | |                           </span>
 1035 | |                         </>
 1036 | |                       )}
 1037 | |                     </p>
 1038 | |                   </div>
 1039 | |   
 1040 | |                   {/* قائمة مصغرة للمهام الأكثر أهمية */}
 1041 | |                   <div className="mt-4">
 1042 | |                     <h3 className="text-lg font-semibold mb-3">المهام الأكثر أهمية</h3>
 1043 | |                     <div className="space-y-2">
 1044 | |                       {/* المهام المعلقة ذات الأولوية */}
 1045 | |                       {report?.blockedTasks && report.blockedTasks.filter(task => task.priority === 'high').length > 0 && (
 1046 | |                         <div className="border rounded-md p-3 bg-red-50">
 1047 | |                           <h4 className="font-medium text-red-700 mb-2">مهام معلقة ذات أولوية عالية</h4>
 1048 | |                           <ul className="space-y-1">
 1049 | |                             {report.blockedTasks
 1050 | |                               .filter(task => task.priority === 'high')
 1051 | |                               .slice(0, 2)
 1052 | |                               .map(task => (
 1053 | |                                 <li key={task.id} className="flex items-center text-sm">
 1054 | |                                   <PauseCircle className="h-4 w-4 text-red-500 ml-2 flex-shrink-0" />
 1055 | |                                   <span className="truncate">{task.description}</span>
 1056 | |                                 </li>
 1057 | |                               ))}
 1058 | |                             {report.blockedTasks.filter(task => task.priority === 'high').length > 2 && (
 1059 | |                               <li className="text-xs text-muted-foreground text-center">
 1060 | |                                 + {report.blockedTasks.filter(task => task.priority === 'high').length - 2} مهام أخرى...
 1061 | |                               </li>
 1062 | |                             )}
 1063 | |                           </ul>
 1064 | |                         </div>
 1065 | |                       )}
 1066 | |   
 1067 | |                       {/* المهام قيد التنفيذ المتأخرة */}
 1068 | |                       {report?.inProgressTasks && report.inProgressTasks.filter(task =>
 1069 | |                         task.dueDate && new Date(task.dueDate) < new Date()
 1070 | |                       ).length > 0 && (
 1071 | |                         <div className="border rounded-md p-3 bg-amber-50">
 1072 | |                           <h4 className="font-medium text-amber-700 mb-2">مهام متأخرة قيد التنفيذ</h4>
 1073 | |                           <ul className="space-y-1">
 1074 | |                             {report.inProgressTasks
 1075 | |                               .filter(task => task.dueDate && new Date(task.dueDate) < new Date())
 1076 | |                               .slice(0, 2)
 1077 | |                               .map(task => (
 1078 | |                                 <li key={task.id} className="flex items-center text-sm">
 1079 | |                                   <Clock className="h-4 w-4 text-amber-500 ml-2 flex-shrink-0" />
 1080 | |                                   <span className="truncate">{task.description}</span>
 1081 | |                                   {task.dueDate && (
 1082 | |                                     <span className="text-xs text-amber-600 mr-auto">
 1083 | |                                       تاريخ الاستحقاق: {formatDate(new Date(task.dueDate))}
 1084 | |                                     </span>
 1085 | |                                   )}
 1086 | |                                 </li>
 1087 | |                               ))}
 1088 | |                             {report.inProgressTasks.filter(task =>
 1089 | |                               task.dueDate && new Date(task.dueDate) < new Date()
 1090 | |                             ).length > 2 && (
 1091 | |                               <li className="text-xs text-muted-foreground text-center">
 1092 | |                                 + {report.inProgressTasks.filter(task =>
 1093 | |                                   task.dueDate && new Date(task.dueDate) < new Date()
 1094 | |                                 ).length - 2} مهام أخرى...
 1095 | |                               </li>
 1096 | |                             )}
 1097 | |                           </ul>
 1098 | |                         </div>
 1099 | |                       )}
 1100 | |   
 1101 | |                       {/* المهام القادمة ذات الأولوية العالية */}
 1102 | |                       {report?.upcomingTasks?.filter(task => task.priority === 'high').length > 0 && (
 1103 | |                         <div className="border rounded-md p-3 bg-blue-50">
 1104 | |                           <h4 className="font-medium text-blue-700 mb-2">مهام قادمة ذات أولوية عالية</h4>
 1105 | |                           <ul className="space-y-1">
 1106 | |                             {report.upcomingTasks
 1107 | |                               .filter(task => task.priority === 'high')
 1108 | |                               .slice(0, 2)
 1109 | |                               .map(task => (
 1110 | |                                 <li key={task.id} className="flex items-center text-sm">
 1111 | |                                   <Calendar className="h-4 w-4 text-blue-500 ml-2 flex-shrink-0" />
 1112 | |                                   <span className="truncate">{task.description}</span>
 1113 | |                                   {task.dueDate && (
 1114 | |                                     <span className="text-xs text-blue-600 mr-auto">
 1115 | |                                       تاريخ الاستحقاق: {formatDate(new Date(task.dueDate))}
 1116 | |                                     </span>
 1117 | |                                   )}
 1118 | |                                 </li>
 1119 | |                               ))}
 1120 | |                             {report.upcomingTasks.filter(task => task.priority === 'high').length > 2 && (
 1121 | |                               <li className="text-xs text-muted-foreground text-center">
 1122 | |                                 + {report.upcomingTasks.filter(task => task.priority === 'high').length - 2} مهام أخرى...
 1123 | |                               </li>
 1124 | |                             )}
 1125 | |                           </ul>
 1126 | |                         </div>
 1127 | |                       )}
 1128 | |   
 1129 | |                       {/* رسالة إذا لم تكن هناك مهام مهمة */}
 1130 | |                       {(!report?.blockedTasks?.filter(task => task.priority === 'high').length &&
 1131 | |                         !report?.inProgressTasks?.filter(task =>
 1132 | |                           task.dueDate && new Date(task.dueDate) < new Date()
 1133 | |                         ).length &&
 1134 | |                         !report?.upcomingTasks?.filter(task => task.priority === 'high').length) && (
 1135 | |                         <p className="text-center text-muted-foreground py-2">
 1136 | |                           لا توجد مهام ذات أولوية عالية أو متأخرة في هذه الفترة.
 1137 | |                         </p>
 1138 | |                       )}
 1139 | |                     </div>
 1140 | |                   </div>
 1141 | |   
 1142 | |                   <h3 className="text-lg font-semibold flex items-center mt-4">
 1143 | |                     <BarChart className="ml-2 h-5 w-5 text-primary" />
 1144 | |                     مؤشرات الأداء الرئيسية
 1145 | |                   </h3>
 1146 | |                   <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
 1147 | |                     <Card>
 1148 | |                       <CardContent className="pt-6">
 1149 | |                         <div className="text-center">
 1150 | |                           <div className="text-2xl font-bold">
 1151 | |                             {report?.keyMetrics?.completionRate !== undefined
 1152 | |                               ? Math.max(1, Math.round(report.keyMetrics.completionRate))
 1153 | |                               : Math.max(1, calculateCompletionRate(report))}%!
(MISSING) 1154 | |                           </div>
 1155 | |                           <p className="text-sm text-muted-foreground">نسبة إكمال المهام</p>
 1156 | |                         </div>
 1157 | |                       </CardContent>
 1158 | |                     </Card>
 1159 | |                     <Card>
 1160 | |                       <CardContent className="pt-6">
 1161 | |                         <div className="text-center">
 1162 | |                           <div className="text-2xl font-bold">
 1163 | |                             {report?.keyMetrics?.onTimeCompletionRate !== undefined
 1164 | |                               ? Math.max(1, Math.round(report.keyMetrics.onTimeCompletionRate))
 1165 | |                               : Math.max(1, calculateOnTimeCompletionRate(tasks))}%!
(MISSING) 1166 | |                           </div>
 1167 | |                           <p className="text-sm text-muted-foreground">نسبة الإكمال في الوقت المحدد</p>
 1168 | |                         </div>
 1169 | |                       </CardContent>
 1170 | |                     </Card>
 1171 | |                     <Card>
 1172 | |                       <CardContent className="pt-6">
 1173 | |                         <div className="text-center">
 1174 | |                           <div className="text-2xl font-bold">
 1175 | |                             {report?.keyMetrics?.averageProgress !== undefined
 1176 | |                               ? Math.max(1, Math.round(report.keyMetrics.averageProgress))
 1177 | |                               : Math.max(1, calculateAverageProgress(report))}%!
(MISSING) 1178 | |                           </div>
 1179 | |                           <p className="text-sm text-muted-foreground">متوسط التقدم</p>
 1180 | |                         </div>
 1181 | |                       </CardContent>
 1182 | |                     </Card>
 1183 | |                   </div>
 1184 | |   
 1185 | |                   <h3 className="text-lg font-semibold mt-4">التوصيات</h3>
 1186 | |                   <ul className="space-y-2">
 1187 | |                     {report?.recommendations && report.recommendations.length > 0 ? (
 1188 | |                       report.recommendations.map((recommendation, index) => (
 1189 | |                         <li key={index} className="flex items-start">
 1190 | |                           <span className="ml-2 text-primary">•</span>
 1191 | |                           <span>{recommendation}</span>
 1192 | |                         </li>
 1193 | |                       ))
 1194 | |                     ) : (
 1195 | |                       // استخدام دالة إنشاء التوصيات الافتراضية
 1196 | |                       generateDefaultRecommendations(report, tasks).map((recommendation, index) => (
 1197 | |                         <li key={index} className="flex items-start">
 1198 | |                           <span className="ml-2 text-primary">•</span>
 1199 | |                           <span>{recommendation}</span>
 1200 | |                         </li>
 1201 | |                       ))
 1202 | |                     )}
 1203 | |                   </ul>
 1204 | |                 </TabsContent>
 1205 | |   
 1206 | |                 <TabsContent value="completed" className="space-y-4">
 1207 | |                   <div className="flex items-center mb-4">
 1208 | |                     <CheckCircle2 className="ml-2 h-5 w-5 text-green-500" />
 1209 | |                     <h3 className="text-lg font-semibold">المهام مكتملة</h3>
 1210 | |                     <Badge className="mr-2 bg-green-100 text-green-800 hover:bg-green-200 border-green-300">
 1211 | |                       {report?.completedTasks?.length || 0}
 1212 | |                     </Badge>
 1213 | |                   </div>
 1214 | |   
 1215 | |                   {!report?.completedTasks?.length ? (
 1216 | |                     <div className="text-center py-4 space-y-2">
 1217 | |                       <p className="text-muted-foreground">
 1218 | |                         لا توجد مهام مكتملة خلال هذه الفترة.
 1219 | |                       </p>
 1220 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1221 | |                         <h4 className="text-sm font-medium mb-2 text-primary">اقتراحات لتحسين معدل إكمال المهام:</h4>
 1222 | |                         <ul className="text-xs text-right space-y-1">
 1223 | |                           <li className="flex items-start">
 1224 | |                             <span className="ml-1 text-primary">•</span>
 1225 | |                             <span>قم بتقسيم المهام الكبيرة إلى مهام أصغر يمكن إنجازها بسهولة.</span>
 1226 | |                           </li>
 1227 | |                           <li className="flex items-start">
 1228 | |                             <span className="ml-1 text-primary">•</span>
 1229 | |                             <span>حدد أوقاتًا محددة في اليوم للعمل على المهام ذات الأولوية.</span>
 1230 | |                           </li>
 1231 | |                           <li className="flex items-start">
 1232 | |                             <span className="ml-1 text-primary">•</span>
 1233 | |                             <span>استخدم تقنية بومودورو (25 دقيقة عمل، 5 دقائق راحة) لزيادة التركيز.</span>
 1234 | |                           </li>
 1235 | |                         </ul>
 1236 | |                       </div>
 1237 | |                     </div>
 1238 | |                   ) : (
 1239 | |                     <div className="space-y-3">
 1240 | |                       {report.completedTasks.map((task) => {
 1241 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1242 | |                         const taskForCard = {
 1243 | |                           id: task.id,
 1244 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1245 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1246 | |                           status: 'completed' as const,
 1247 | |                           progress: 100,
 1248 | |                           priority: typeof task.priority === 'string' ?
 1249 | |                             task.priority === 'high' ? 'high' :
 1250 | |                             task.priority === 'medium' ? 'medium' :
 1251 | |                             task.priority === 'low' ? 'low' : 'medium'
 1252 | |                             : task.priority as any,
 1253 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
 1254 | |                           completedDate: task.completedDate ? new Date(task.completedDate) : undefined
 1255 | |                         } as TaskType;
 1256 | |   
 1257 | |                         return (
 1258 | |                           <TaskCardTemp
 1259 | |                             key={task.id}
 1260 | |                             id={task.id}
 1261 | |                             task={taskForCard}
 1262 | |                             aiReasoning={`تم إكمال هذه المهمة في ${task.completedDate ? format(new Date(task.completedDate), 'dd/MM/yyyy', { locale: ar }) : 'هذا الأسبوع'}`}
 1263 | |                           />
 1264 | |                         );
 1265 | |                       })}
 1266 | |                     </div>
 1267 | |                   )}
 1268 | |                 </TabsContent>
 1269 | |   
 1270 | |                 <TabsContent value="inProgress" className="space-y-4">
 1271 | |                   <div className="flex items-center mb-4">
 1272 | |                     <Clock className="ml-2 h-5 w-5 text-blue-500" />
 1273 | |                     <h3 className="text-lg font-semibold">المهام قيد الانتظار</h3>
 1274 | |                     <Badge className="mr-2 bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">
 1275 | |                       {report?.inProgressTasks?.length || 0}
 1276 | |                     </Badge>
 1277 | |                   </div>
 1278 | |   
 1279 | |                   {!report?.inProgressTasks?.length ? (
 1280 | |                     <div className="text-center py-4 space-y-2">
 1281 | |                       <p className="text-muted-foreground">
 1282 | |                         لا توجد مهام قيد الانتظار.
 1283 | |                       </p>
 1284 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1285 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح لإدارة المهام قيد الانتظار:</h4>
 1286 | |                         <ul className="text-xs text-right space-y-1">
 1287 | |                           <li className="flex items-start">
 1288 | |                             <span className="ml-1 text-primary">•</span>
 1289 | |                             <span>ابدأ بالمهام ذات الأولوية العالية أو المواعيد النهائية القريبة.</span>
 1290 | |                           </li>
 1291 | |                           <li className="flex items-start">
 1292 | |                             <span className="ml-1 text-primary">•</span>
 1293 | |                             <span>قم بتحديث نسبة التقدم بانتظام لتتبع الإنجاز.</span>
 1294 | |                           </li>
 1295 | |                           <li className="flex items-start">
 1296 | |                             <span className="ml-1 text-primary">•</span>
 1297 | |                             <span>حدد العقبات التي تواجهك مبكرًا وابحث عن حلول لها.</span>
 1298 | |                           </li>
 1299 | |                         </ul>
 1300 | |                       </div>
 1301 | |                     </div>
 1302 | |                   ) : (
 1303 | |                     <div className="space-y-3">
 1304 | |                       {report.inProgressTasks.map((task) => {
 1305 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1306 | |                         const taskForCard = {
 1307 | |                           id: task.id,
 1308 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1309 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1310 | |                           status: 'in-progress' as any,
 1311 | |                           progress: task.progress || 50,
 1312 | |                           priority: typeof task.priority === 'string' ?
 1313 | |                             task.priority === 'high' ? 'high' :
 1314 | |                             task.priority === 'medium' ? 'medium' :
 1315 | |                             task.priority === 'low' ? 'low' : 'medium'
 1316 | |                             : task.priority as any,
 1317 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1318 | |                         } as TaskType;
 1319 | |   
 1320 | |                         const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();
 1321 | |                         const reasoning = isOverdue
 1322 | |                           ? `مهمة متأخرة - تاريخ الاستحقاق: ${task.dueDate ? format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar }) : 'غير محدد'}`
 1323 | |                           : `نسبة التقدم: ${task.progress || 0}%!`(MISSING);
 1324 | |   
 1325 | |                         return (
 1326 | |                           <TaskCardTemp
 1327 | |                             key={task.id}
 1328 | |                             id={task.id}
 1329 | |                             task={taskForCard}
 1330 | |                             aiReasoning={reasoning}
 1331 | |                           />
 1332 | |                         );
 1333 | |                       })}
 1334 | |                     </div>
 1335 | |                   )}
 1336 | |                 </TabsContent>
 1337 | |   
 1338 | |                 <TabsContent value="upcoming" className="space-y-4">
 1339 | |                   <div className="flex items-center mb-4">
 1340 | |                     <Calendar className="ml-2 h-5 w-5 text-orange-500" />
 1341 | |                     <h3 className="text-lg font-semibold">المهام قادمة</h
3>
 1342 | |                     <Badge className="mr-2 bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-300">
 1343 | |                       {report?.upcomingTasks?.length || 0}
 1344 | |                     </Badge>
 1345 | |                   </div>
 1346 | |   
 1347 | |                   {!report?.upcomingTasks?.length ? (
 1348 | |                     <div className="text-center py-4 space-y-2">
 1349 | |                       <p className="text-muted-foreground">
 1350 | |                         لا توجد مهام قادمة.
 1351 | |                       </p>
 1352 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1353 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتخطيط للمهام القادمة:</h4>
 1354 | |                         <ul className="text-xs text-right space-y-1">
 1355 | |                           <li className="flex items-start">
 1356 | |                             <span className="ml-1 text-primary">•</span>
 1357 | |                             <span>قم بتحديد أولويات المهام القادمة بناءً على أهميتها وتاريخ استحقاقها.</span>
 1358 | |                           </li>
 1359 | |                           <li className="flex items-start">
 1360 | |                             <span className="ml-1 text-primary">•</span>
 1361 | |                             <span>خطط للمهام الكبيرة مبكرًا وقسمها إلى مهام فرعية أصغر.</span>
 1362 | |                           </li>
 1363 | |                           <li className="flex items-start">
 1364 | |                             <span className="ml-1 text-primary">•</span>
 1365 | |                             <span>ضع تواريخ بدء للمهام القادمة لتجنب تراكمها قرب موعد الاستحقاق.</span>
 1366 | |                           </li>
 1367 | |                         </ul>
 1368 | |                       </div>
 1369 | |                     </div>
 1370 | |                   ) : (
 1371 | |                     <div className="space-y-3">
 1372 | |                       {report.upcomingTasks.map((task) => {
 1373 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1374 | |                         const taskForCard = {
 1375 | |                           id: task.id,
 1376 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1377 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1378 | |                           status: 'pending',
 1379 | |                           progress: task.progress || 0,
 1380 | |                           priority: typeof task.priority === 'string' ?
 1381 | |           
                  task.priority === 'high' ? 'high' :
 1382 | |                             task.priority === 'medium' ? 'medium' :
 1383 | |                             task.priority === 'low' ? 'low' : 'medium'
 1384 | |                             : task.priority as any,
 1385 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1386 | |                         } as TaskType;
 1387 | |   
 1388 | |                         return (
 1389 | |                           <TaskCardTemp
 1390 | |                             key={task.id}
 1391 | |                             id={task.id}
 1392 | |                             task={taskForCard}
 1393 | |                             aiReasoning={`تاريخ الاستحقاق: ${task.dueDate ? format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar }) : 'غير محدد'}`}
 1394 | |                           />
 1395 | |                         );
 1396 | |                       })}
 1397 | |                     </div>
 1398 | |                   )}
 1399 | |                 </TabsContent>
 1400 | |   
 1401 | |                 <TabsContent value="planning" className="space-y-4">
 1402 | |                   <div className="bg-muted/30 p-4 rounded-lg">
 1403 | |                     <h3 className="text-lg font-semibold mb-3">التخطيط للأسبوع القادم</h3>
 1404 | |                     <p className="text-right leading-7 mb-4">
 1405 | |                       بناءً على تحليل أدائك في الأسبوع الحالي، إليك خطة عمل مقترحة للأسبوع القادم لتحسين إنتاجيتك وتحقيق أهدافك.
 1406 | |                     </p>
 1407 | |                   </div>
 1408 | |   
 1409 | |                   {/* الأولويات المقترحة */}
 1410 | |                   <Card className="mt-4">
 1411 | |                     <CardHeader>
 1412 | |                       <CardTitle>الأولويات المقترحة للأسبوع القادم</CardTitle>
 1413 | |                       <CardDescription>
 1414 | |                         مهام يجب التركيز عليها في الأسبوع القادم
 1415 | |                       </CardDescription>
 1416 | |                     </CardHeader>
 1417 | |                     <CardContent>
 1418 | |                       <div className="space-y-4">
 1419 | |                         {/* المهام المتأخرة */}
 1420 | |                         {report?.inProgressTasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length ? (
 1421 | |                           <div>
 1422 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1423 | |                               <AlertTriangle className="h-4 w-4 text-red-500 ml-2" />
 1424 | |                               المهام المتأخرة
 1425 | |                             </h4>
 1426 | |                             <ul className="space-y-1 text-sm pr-5">
 1427 | |                               {report.inProgressTasks
 1428 | |                                 .filter(t => t.dueDate && new Date(t.dueDate) < new Date())
 1429 | |                                 .map((task, index) => (
 1430 | |                                   <li key={index} className="list-disc">
 1431 | |                                     {task.description || task.title} - متأخرة منذ {task.dueDate ? Math.ceil((new Date().getTime() - new Date(task.dueDate).getTime()) / (1000 * 60 * 60 * 24)) : 0} أيام
 1432 | |                                   </li>
 1433 | |                                 ))}
 1434 | |                             </ul>
 1435 | |                           </div>
 1436 | |                         ) : null}
 1437 | |   
 1438 | |                         {/* المهام المعلقة */}
 1439 | |                         {report?.blockedTasks?.length ? (
 1440 | |                           <div>
 1441 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1442 | |                               <PauseCircle className="h-4 w-4 text-gray-500 ml-2" />
 1443 | |                               المهام المعلقة
 1444 | |                             </h4>
 1445 | |                             <ul className="space-y-1 text-sm pr-5">
 1446 | |                               {report.blockedTasks
 1447 | |                                 .slice(0, 3)
 1448 | |                                 .map((task, index) => (
 1449 | |                                   <li key={index} className="list-disc">
 1450 | |                                     {task.description || task.title} - تحديد العقبات وإيجاد حلول
 1451 | |                                   </li>
 1452 | |                                 ))}
 1453 | |                               {report.blockedTasks.length > 3 && (
 1454 | |                                 <li className="text-xs text-muted-foreground">
 1455 | |                                   + {report.blockedTasks.length - 3} مهام معلقة أخرى...
 1456 | |                                 </li>
 1457 | |                               )}
 1458 | |                             </ul>
 1459 | |                           </div>
 1460 | |                         ) : null}
 1461 | |   
 1462 | |                         {/* المهام ذات الأولوية العالية */}
 1463 | |                         {report?.upcomingTasks?.filter(t => t.priority === 'high').length ? (
 1464 | |                           <div>
 1465 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1466 | |                               <span className="inline-block w-3 h-3 rounded-full bg-red-500 ml-2"></span>
 1467 | |                               المهام ذات الأولوية العالية
 1468 | |                             </h4>
 1469 | |                             <ul className="space-y-1 text-sm pr-5">
 1470 | |                               {report.upcomingTasks
 1471 | |                                 .filter(t => t.priority === 'high')
 1472 | |                                 .slice(0, 3)
 1473 | |                                 .map((task, index) => (
 1474 | |                                   <li key={index} className="list-disc">
 1475 | |                                     {task.description || task.title}
 1476 | |                                     {task.dueDate ? ` - تاريخ الاستحقاق: ${format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar })}` : ''}
 1477 | |                                   </li>
 1478 | |                                 ))}
 1479 | |                               {report.upcomingTasks.filter(t => t.priority === 'high').length > 3 && (
 1480 | |                                 <li className="text-xs text-muted-foreground">
 1481 | |                                   + {report.upcomingTasks.filter(t => t.priority === 'high').length - 3} مهام أخرى ذات أولوية عالية...
 1482 | |                                 </li>
 1483 | |                               )}
 1484 | |                             </ul>
 1485 | |                           </div>
 1486 | |                         ) : null}
 1487 | |   
 1488 | |                         {/* المهام القريبة من الموعد النهائي */}
 1489 | |                         {report?.upcomingTasks?.filter(t =>
 1490 | |                           t.dueDate &&
 1491 | |                           new Date(t.dueDate) > new Date() &&
 1492 | |                           (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1493 | |                         ).length ? (
 1494 | |                           <div>
 1495 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1496 | |                               <Clock className="h-4 w-4 text-amber-500 ml-2" />
 1497 | |                               المهام القريبة من الموعد النهائي
 1498 | |                             </h4>
 1499 | |                             <ul className="space-y-1 text-sm pr-5">
 1500 | |                               {report.upcomingTasks
 1501 | |                                 .filter(t =>
 1502 | |                                   t.dueDate &&
 1503 | |                                   new Date(t.dueDate) > new Date() &&
 1504 | |                                   (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1505 | |                                 )
 1506 | |                                 .slice(0, 3)
 1507 | |                                 .map((task, index) => (
 1508 | |                                   <li key={index} className="list-disc">
 1509 | |                                     {task.description || task.title} - متبقي {task.dueDate ? Math.ceil((new Date(task.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0} أيام
 1510 | |                                   </li>
 1511 | |                                 ))}
 1512 | |                               {report.upcomingTasks.filter(t =>
 1513 | |                                 t.dueDate &&
 1514 | |                                 new Date(t.dueDate) > new Date() &&
 1515 | |                                 (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1516 | |                               ).length > 3 && (
 1517 | |                                 <li className="text-xs text-muted-foreground">
 1518 | |                                   + {report.upcomingTasks.filter(t =>
 1519 | |                                     t.dueDate &&
 1520 | |                                     new Date(t.dueDate) > new Date() &&
 1521 | |                                     (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1522 | |                                   ).length - 3} مهام أخرى قريبة من الموعد النهائي...
 1523 | |                                 </li>
 1524 | |                               )}
 1525 | |                             </ul>
 1526 | |                           </div>
 1527 | |                         ) : null}
 1528 | |                       </div>
 1529 | |                     </CardContent>
 1530 | |                   </Card>
 1531 | |   
 1532 | |                   {/* استراتيجيات مقترحة */}
 1533 | |                   <Card className="mt-4">
 1534 | |                     <CardHeader>
 1535 | |                       <CardTitle>استراتيجيات مقترحة لتحسين الإنتاجية</CardTitle>
 1536 | |                     </CardHeader>
 1537 | |                     <CardContent>
 1538 | |                       <div className="space-y-4">
 1539 | |                         {/* استراتيجيات مخصصة بناءً على التحليل */}
 1540 | |                         <div className="space-y-2">
 1541 | |                           {report?.keyMetrics?.completionRate && report.keyMetrics.completionRate < 50 ? (
 1542 | |                             <div className="bg-blue-50 p-3 rounded-md">
 1543 | |                               <h4 className="text-sm font-semibold text-blue-700 mb-2">تحسين معدل إكمال المهام</h4>
 1544 | |                               <ul className="space-y-1 text-sm pr-5">
 1545 | |                                 <li className="list-disc">تقسيم المهام الكبيرة إلى مهام فرعية أصغر يمكن إنجازها بسهولة.</li>
 1546 | |                                 <li className="list-disc">تخصيص وقت محدد يوميًا للعمل على المهام ذات الأولوية العالية.</li>
 1547 | |                                 <li className="list-disc">استخدام تقنية بومودورو (25 دقيقة عمل، 5 دقائق راحة) لزيادة التركيز.</li>
 1548 | |                               </ul>
 1549 | |                             </div>
 1550 | |                           ) : null}
 1551 | |   
 1552 | |                           {report?.keyMetrics?.onTimeCompletionRate && report.keyMetrics.onTimeCompletionRate < 70 ? (
 1553 | |                             <div className="bg-amber-50 p-3 rounded-md">
 1554 | |                               <h4 className="text-sm font-semibold text-amber-700 mb-2">تحسين الالتزام بالمواعيد النهائية</h4>
 1555 | |                               <ul className="space-y-1 text-sm pr-5">
 1556 | |                                 <li className="list-disc">تحديد مواعيد نهائية واقعية للمهام مع إضافة وقت احتياطي.</li>
 1557 | |                                 <li className="list-disc">تقسيم المهام الكبيرة إلى مراحل مع مواعيد نهائية لكل مرحلة.</li>
 1558 | |                                 <li className="list-disc">البدء في المهام مبكرًا وعدم تأجيلها حتى اقتراب الموعد النهائي.</li>
 1559 | |                               </ul>
 1560 | |                             </div>
 1561 | |                           ) : null}
 1562 | |   
 1563 | |                           {report?.blockedTasks?.length ? (
 1564 | |                             <div className="bg-gray-50 p-3 rounded-md">
 1565 | |                               <h4 className="text-sm font-semibold text-gray-700 mb-2">التعامل مع المهام المعلقة</h4>
 1566 | |                               <ul className="space-y-1 text-sm pr-5">
 1567 | |                                 <li className="list-disc">تحديد العقبات التي تعيق كل مهمة بوضوح.</li>
 1568 | |                                 <li className="list-disc">طلب المساعدة أو الموارد اللازمة لتجاوز العقبات.</li>
 1569 | |                                 <li className="list-disc">إعادة تقييم المهام المعلقة لفترة طويلة وتحديد ما إذا كانت لا تزال ضرورية.</li>
 1570 | |                               </ul>
 1571 | |                             </div>
 1572 | |                           ) : null}
 1573 | |   
 1574 | |                           {report?.upcomingTasks?.length > 5 ? (
 1575 | |                             <div className="bg-green-50 p-3 rounded-md">
 1576 | |                               <h4 className="text-sm font-semibold text-green-700 mb-2">إدارة المهام القادمة</h4>
 1577 | |                               <ul className="space-y-1 text-sm pr-5">
 1578 | |                                 <li className="list-disc">ترتيب المهام القادمة حسب الأولوية والموعد النهائي.</li>
 1579 | |                                 <li className="list-disc">تحديد المهام التي يمكن تفويضها أو تأجيلها إذا لزم الأمر.</li>
 1580 | |                                 <li className="list-disc">وضع خطة زمنية واضحة للبدء في المهام القادمة.</li>
 1581 | |                               </ul>
 1582 | |                             </div>
 1583 | |                           ) : null}
 1584 | |                         </div>
 1585 | |                       </div>
 1586 | |                     </CardContent>
 1587 | |                   </Card>
 1588 | |   
 1589 | |                   {/* خطة الأسبوع القادم */}
 1590 | |                   <Card className="mt-4">
 1591 | |                     <CardHeader>
 1592 | |                       <CardTitle>خطة الأسبوع القادم</CardTitle>
 1593 | |                       <CardDescription>
 1594 | |                         توزيع مقترح للمهام على أيام الأسبوع القادم
 1595 | |                       </CardDescription>
 1596 | |                     </CardHeader>
 1597 | |                     <CardContent>
 1598 | |                       <div className="space-y-4">
 1599 | |                         {/* توزيع المهام على أيام الأسبوع */}
 1600 | |                         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
 1601 | |                           {/* اليوم الأول والثاني */}
 1602 | |                           <div className="space-y-3">
 1603 | |                             <div>
 1604 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الأول والثاني</h4>
 1605 | |                               <ul className="space-y-1 text-sm pr-5">
 1606 | |                                 <li className="list-disc">التركيز على المهام المتأخرة والمعلقة.</li>
 1607 | |                                 {report?.inProgressTasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length ? (
 1608 | |                                   <li className="list-disc">إكمال {Math.min(report.inProgressTasks.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length, 2)} مهام متأخرة.</li>
 1609 | |                                 ) : null}
 1610 | |                                 {report?.blockedTasks?.length ? (
 1611 | |                                   <li className="list-disc">حل العقبات في {Math.min(report.blockedTasks.length, 2)} مهام معلقة.</li>
 1612 | |                                 ) : null}
 1613 | |                                 <li className="list-disc">مراجعة وتحديث خطة العمل الأسبوعية.</li>
 1614 | |                               </ul>
 1615 | |                             </div>
 1616 | |                             <div>
 1617 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الثالث والرابع</h4>
 1618 | |                               <ul className="space-y-1 text-sm pr-5">
 1619 | |                                 <li className="list-disc">التركيز على المهام ذات الأولوية العالية.</li>
 1620 | |                                 {report?.upcomingTasks?.filter(t => t.priority === 'high').length ? (
 1621 | |                                   <li className="list-disc">البدء في {Math.min(report.upcomingTasks.filter(t => t.priority === 'high').length, 2)} مهام ذات أولوية عالية.</li>
 1622 | |                                 ) : null}
 1623 | |                                 {report?.inProgressTasks?.length ? (
 1624 | |                                   <li className="list-disc">متابعة التقدم في المهام قيد التنفيذ.</li>
 1625 | |                                 ) : null}
 1626 | |                                 <li className="list-disc">تحديث نسب التقدم في المهام.</li>
 1627 | |                               </ul>
 1628 | |                             </div>
 1629 | |                           </div>
 1630 | |                           {/* اليوم الخامس والسادس والسابع */}
 1631 | |                           <div className="space-y-3">
 1632 | |                             <div>
 1633 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الخامس والسادس</h4>
 1634 | |                               <ul className="space-y-1 text-sm pr-5">
 1635 | |                                 <li className="list-disc">التركيز على المهام القريبة من الموعد النهائي.</li>
 1636 | |                                 {report?.upcomingTasks?.filter(t =>
 1637 | |                                   t.dueDate &&
 1638 | |                                   new Date(t.dueDate) > new Date() &&
 1639 | |                                   (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1640 | |                                 ).length ? (
 1641 | |                                   <li className="list-disc">العمل على {Math.min(report.upcomingTasks.filter(t =>
 1642 | |                                     t.dueDate &&
 1643 | |                                     new Date(t.dueDate) > new Date() &&
 1644 | |                                     (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1645 | |                                   ).length, 2)} مهام قريبة من الموعد النهائي.</li>
 1646 | |                                 ) : null}
 1647 | |                                 <li className="list-disc">إكمال المهام قيد التنفيذ قدر الإمكان.</li>
 1648 | |                               </ul>
 1649 | |                             </div>
 1650 | |                             <div>
 1651 | |                               <h4 className="text-sm font-semibold mb-2">اليوم السابع</h4>
 1652 | |                               <ul className="space-y-1 text-sm pr-5">
 1653 | |                                 <li className="list-disc">مراجعة ما تم إنجازه خلال الأسبوع.</li>
 1654 | |                                 <li className="list-disc">تحديث حالة جميع المهام.</li>
 1655 | |                                 <li className="list-disc">التخطيط للأسبوع القادم.</li>
 1656 | |                                 <li className="list-disc">تحديد الدروس المستفادة وفرص التحسين.</li>
 1657 | |                               </ul>
 1658 | |                             </div>
 1659 | |                           </div>
 1660 | |                         </div>
 1661 | |                       </div>
 1662 | |                     </CardContent>
 1663 | |                   </Card>
 1664 | |                 </TabsContent>
 1665 | |   
 1666 | |                 <TabsContent value="blocked" className="space-y-4">
 1667 | |                   <div className="flex items-center mb-4">
 1668 | |                     <PauseCircle className="ml-2 h-5 w-5 text-gray-500" />
 1669 | |                     <h3 className="text-lg font-semibold">المهام معلقة</h3>
 1670 | |                     <Badge className="mr-2 bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-300">
 1671 | |                       {report?.blockedTasks?.length || 0}
 1672 | |                     </Badge>
 1673 | |                   </div>
 1674 | |   
 1675 | |                   {!report?.blockedTasks?.length ? (
 1676 | |                     <div className="text-center py-4 space-y-2">
 1677 | |                       <p className="text-muted-foreground">
 1678 | |                         لا توجد مهام معلقة.
 1679 | |                       </p>
 1680 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1681 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتعامل مع المهام معلقة:</h4>
 1682 | |                         <ul className="text-xs text-right space-y-1">
 1683 | |                           <li className="flex items-start">
 1684 | |                             <span className="ml-1 text-primary">•</span>
 1685 | |                             <span>حدد بوضوح سبب تعليق كل مهمة وما هي العقبات التي تواجهها.</span>
 1686 | |                           </li>
 1687 | |                           <li className="flex items-start">
 1688 | |                             <span className="ml-1 text-primary">•</span>
 1689 | |                             <span>ضع خطة عمل لإزالة العقبات وإعادة تنشيط المهام المعلقة.</span>
 1690 | |                           </li>
 1691 | |                           <li className="flex items-start">
 1692 | |                             <span className="ml-1 text-primary">•</span>
 1693 | |                             <span>راجع المهام المعلقة بانتظام لتحديد ما إذا كانت لا تزال ذات صلة أو يجب إلغاؤها.</span>
 1694 | |                           </li>
 1695 | |                         </ul>
 1696 | |                       </div>
 1697 | |                     </div>
 1698 | |                   ) : (
 1699 | |                     <div className="space-y-3">
 1700 | |                       {report.blockedTasks.map((task) => {
 1701 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1702 | |                         const taskForCard = {
 1703 | |                           id: task.id,
 1704 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1705 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1706 | |                           status: 'pending',
 1707 | |                           progress: task.progress || 0,
 1708 | |                           priority: typeof task.priority === 'string' ?
 1709 | |                             task.priority === 'high' ? 'high' :
 1710 | |                             task.priority === 'medium' ? 'medium' :
 1711 | |                             task.priority === 'low' ? 'low' : 'medium'
 1712 | |                             : task.priority as any,
 1713 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1714 | |                         } as TaskType;
 1715 | |   
 1716 | |                         return (
 1717 | |                           <TaskCardTemp
 1718 | |                             key={task.id}
 1719 | |                             id={task.id}
 1720 | |                             task={taskForCard}
 1721 | |                             aiReasoning={task.comment || "هذه المهمة معلقة وتحتاج إلى مراجعة"}
 1722 | |                           />
 1723 | |[
0m                         );
 1724 | |                       })}
 1725 | |                     </div>
 1726 | |                   )}
 1727 | |                 </TabsContent>
 1728 | |   
 1729 | |                 <TabsContent value="overdue" className="space-y-4">
 1730 | |                   <div className="flex items-center mb-4">
 1731 | |                     <AlertTriangle className="ml-2 h-5 w-5 text-red-500" />
 1732 | |                     <h3 className="text-lg font-semibold">المهام فائتة</h3>
 1733 | |                     <Badge className="mr-2 bg-red-100 text-red-800 hover:bg-red-200 border-red-300">
 1734 | |                       {report?.overdueTasks?.length || 0}
 1735 | |                     </Badge>
 1736 | |                   </div>
 1737 | |   
 1738 | |                   {!report?.overdueTasks?.length ? (
 1739 | |                     <div className="text-center py-4 space-y-2">
 1740 | |                       <p className="text-muted-foreground">
 1741 | |                         لا توجد مهام فائتة.
 1742 | |                       </p>
 1743 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1744 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتعامل مع المهام الفائتة:</h4>
 1745 | |                         <ul className="text-xs text-right space-y-1">
 1746 | |                           <li className="flex items-start">
 1747 | |                             <span className="ml-1 text-primary">•</span>
 1748 | |                             <span>حدد أولويات المهام الفائتة وابدأ بالأكثر أهمية.</span>
 1749 | |                           </li>
 1750 | |                           <li className="flex items-start">
 1751 | |                             <span className="ml-1 text-primary">•</span>
 1752 | |                             <span>قم بتحديث المواعيد النهائية إذا كانت المهام لا تزال ذات صلة.</span>
 1753 | |                           </li>
 1754 | |                           <li className="flex items-start">
 1755 | |                             <span className="ml-1 text-primary">•</span>
 1756 | |                             <span>حلل أسباب التأخير لتجنب تكرارها في المستقبل.</span>
 1757 | |                           </li>
 1758 | |                         </ul>
 1759 | |                       </div>
 1760 | |                     </div>
 1761 | |                   ) : (
 1762 | |                     <div className="space-y-3">
 1763 | |                       {report.overdueTasks.map((task) => {
 1764 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1765 | |                         const taskForCard = {
 1766 | |                           id: task.id,
 1767 | [3
5;1m|                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1768 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1769 | |                           status: 'pending',
 1770 | |                           progress: task.progress || 0,
 1771 | |                           priority: typeof task.priority === 'string' ?
 1772 | |                             task.priority === 'high' ? 'high' :
 1773 | |                             task.priority === 'medium' ? 'medium' :
 1774 | |                             task.priority === 'low' ? 'low' : 'medium'
 1775 | |                             : task.priority as any,
 1776 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1777 | |                         } as TaskType;
 1778 | |   
 1779 | |                         return (
 1780 | |                           <TaskCardTemp
 1781 | |                             key={task.id}
 1782 | |                             id={task.id}
 1783 | |                             task={taskForCard}
 1784 | |                             aiReasoning={`مهمة فائتة - تاريخ الاستحقاق: ${task.dueDate ? format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar }) : 'غير محدد'}`}
 1785 | |                           />
 1786 | |                         );
 1787 | |                       })}
 1788 | |                     </div>
 1789 | |                   )}
 1790 | |                 </TabsContent>
 1791 | |   
 1792 | |                 {/* تبويب الرسوم البيانية */}
 1793 | |                 <TabsContent value="charts" className="space-y-4">
 1794 | |                   <WeeklyReportCharts
 1795 | |                     completedTasks={report?.completedTasks}
 1796 | |                     inProgressTasks={report?.inProgressTasks}
 1797 | |                     upcomingTasks={report?.upcomingTasks}
 1798 | |                     blockedTasks={report?.blockedTasks}
 1799 | |                     overdueTasks={report?.overdueTasks}
 1800 | |                     keyMetrics={report?.keyMetrics}
 1801 | |                   />
 1802 | |                 </TabsContent>
 1803 | |   
 1804 | |                 {/* تبويب تحليل الاتجاهات */}
 1805 | |                 <TabsContent value="trends" className="space-y-4">
 1806 | |                   {weeklyComparison ? (
 1807 | |                     <WeeklyTrendAnalysis comparison={weeklyComparison} />
 1808 | |                   ) : (
 1809 | |                     <div className="text-center py-8">
 1810 | |                       <p className="text-muted-foreground">جاري تحميل بيانات المقارنة...</p>
 1811 | |                     </div>
 1812 | |                   )}
 1813 | |                 </TabsContent>
 1814 | |   
 1815 | |                 {/* تبويب تحليل الأقسام */}
 1816 | |                 <TabsContent value="departments" className="space-y-4">
 1817 | |                   {departmentData.length > 0 ? (
 1818 | |                     <DepartmentAnalysis departments={departmentData} />
 1819 | |                   ) : (
 1820 | |                     <div className="text-center py-8">
 1821 | |                       <p className="text-muted-foreground">لا توجد بيانات أقسام متاحة أو جاري التحميل...</p>
 1822 | |                     </div>
 1823 | |                   )}
 1824 | |                 </TabsContent>
 1825 | |   
 1826 | |                 {/* تبويب التصدير */}
 1827 | |                 <TabsContent value="export" className="space-y-4">
 1828 | |                   <AdvancedExport
 1829 | |                     data={{
 1830 | |                       title: `التقرير الأسبوعي - ${formatDate(reportPeriod.startDate)} إلى ${formatDate(reportPeriod.endDate)}`,
 1831 | |                       summary: report?.summary || '',
 1832 | |                       completedTasks: report?.completedTasks || [],
 1833 | |                       inProgressTasks: report?.inProgressTasks || [],
 1834 | |                       upcomingTasks: report?.upcomingTasks || [],
 1835 | |                       blockedTasks: report?.blockedTasks || [],
 1836 | |                       keyMetrics: report?.keyMetrics || {
 1837 | |                         completionRate: 0,
 1838 | |                         onTimeCompletionRate: 0,
 1839 | |                         averageProgress: 0
 1840 | |                       },
 1841 | |                       recommendations: report?.recommendations || [],
 1842 | |                       departmentData: departmentData
 1843 | |                     }}
 1844 | |                     reportElement={reportElementRef}
 1845 | |                   />
 1846 | |                 </TabsContent>
 1847 | |               </Tabs>
 1848 | |             </>
 1849 | |           )}
 1850 | |         </CardContent>
 1851 | |       </Card>
 1852 | `->   );
 1853 |     }
 1854 |     
      `----
  x Expression expected
      ,-[/workspace/src/components/WeeklyReportCard.tsx:1853:1]
 1850 |       </CardContent>
 1851 |     </Card>
 1852 |   );
 1853 | }
      : ^
 1854 | 
      `----
Caused by:
    Syntax Error
Import trace for requested module:
./src/components/WeeklyReportCard.tsx
./src/app/(app)/reports/weekly/page.tsx
> Build failed because of webpack errors
/layers/google.nodejs.firebasenextjs/npm_modules/node_modules/@apphosting/common/dist/index.js:36
                reject(new Error(`Build process exited with error code ${code}.`));
                       ^
Error: Build process exited with error code 1.
    at ChildProcess.<anonymous> (/layers/google.nodejs.firebasenextjs/npm_modules/node_modules/@apphosting/common/dist/index.js:36:24)
    at ChildProcess.emit (node:events:524:28)
    at ChildProcess._handle.onexit (node:internal/child_process:293:12)
Node.js v20.19.2
Done "npm exec --prefix /layers/google.nodejs.firebasenextjs/npm_m..." (58.977216083s)
--------------------------------------------------------------------------------
failed to build: (error ID: 20f158fc):
{"reason":"Failed Framework Build","code":"fah/failed-framework-build","userFacingMessage":"Your application failed to run the framework build command 'npm exec --prefix /layers/google.nodejs.firebasenextjs/npm_modules apphosting-adapter-nextjs-build' successfully. Please check the raw log to address the error and confirm that your application builds locally before redeploying.","rawLog":"(error ID: d0a693a9):
...                      task={taskForCard}
 1330 | |                             aiReasoning={reasoning}
 1331 | |                           />
 1332 | |                         );
 1333 | |                       })}
 1334 | |                     </div>
 1335 | |                   )}
 1336 | |                 </TabsContent>
 1337 | |   
 1338 | |                 <TabsContent value="upcoming" className="space-y-4">
 1339 | |                   <div className="flex items-center mb-4">
 1340 | |                     <Calendar className="ml-2 h-5 w-5 text-orange-500" />
 1341 | |                     <h3 className="text-lg font-semibold">المهام قادمة</h3>
 1342 | |                     <Badge className="mr-2 bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-300">
 1343 | |                       {report?.upcomingTasks?.length || 0}
 1344 | |                     </Badge>
 1345 | |                   </div>
 1346 | |   
 1347 | |                   {!report?.upcomingTasks?.length ? (
 1348 | |                     <div className="text-center py-4 space-y-2">
 1349 | |                       <p className="text-muted-foreground">
 1350 | |                         لا توجد مهام قادمة.
 1351 | |                       </p>
 1352 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1353 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتخطيط للمهام القادمة:</h4>
 1354 | |                         <ul className="text-xs text-right space-y-1">
 1355 | |                           <li className="flex items-start">
 1356 | |                             <span className="ml-1 text-primary">•</span>
 1357 | |                             <span>قم بتحديد أولويات المهام القادمة بناءً على أهميتها وتاريخ استحقاقها.</span>
 1358 | |                           </li>
 1359 | |                           <li className="flex items-start">
 1360 | |                             <span className="ml-1 text-primary">•</span>
 1361 | |                             <span>خطط للمهام الكبيرة مبكرًا وقسمها إلى مهام فرعية أصغر.</span>
 1362 | |                           </li>
 1363 | |                           <li className="flex items-start">
 1364 | |                             <span className="ml-1 text-primary">•</span>
 1365 | |                             <span>ضع تواريخ بدء للمهام القادمة لتجنب تراكمها قرب موعد الاستحقاق.</span>
 1366 | |                           </li>
 1367 | |                         </ul>
 1368 | |                       </div>
 1369 | |                     </div>
 1370 | |                   ) : (
 1371 | |                     <div className="space-y-3">
 1372 | |                       {report.upcomingTasks.map((task) => {
 1373 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1374 | |                         const taskForCard = {
 1375 | |                           id: task.id,
 1376 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1377 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1378 | |                           status: 'pending',
 1379 | |                           progress: task.progress || 0,
 1380 | |                           priority: typeof task.priority === 'string' ?
 1381 | |                             task.priority === 'high' ? 'high' :
 1382 | |                             task.priority === 'medium' ? 'medium' :
 1383 | |                             task.priority === 'low' ? 'low' : 'medium'
 1384 | |                             : task.priority as any,
 1385 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1386 | |                         } as TaskType;
 1387 | |   
 1388 | |                         return (
 1389 | |                           <TaskCardTemp
 1390 | |                             key={task.id}
 1391 | |                             id={task.id}
 1392 | |                             task={taskForCard}
 1393 | |                             aiReasoning={`تاريخ الاستحقاق: ${task.dueDate ? format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar }) : 'غير محدد'}`}
 1394 | |                           />
 1395 | |                         );
 1396 | |                       })}
 1397 | |                     </div>
 1398 | |                   )}
 1399 | |                 </TabsContent>
 1400 | |   
 1401 | |                 <TabsContent value="planning" className="space-y-4">
 1402 | |                   <div className="bg-muted/30 p-4 rounded-lg">
 1403 | |                     <h3 className="text-lg font-semibold mb-3">التخطيط للأسبوع القادم</h3>
 1404 | |                     <p className="text-right leading-7 mb-4">
 1405 | |                       بناءً على تحليل أدائك في الأسبوع الحالي، إليك خطة عمل مقترحة للأسبوع القادم لتحسين إنتاجيتك وتحقيق أهدافك.
 1406 | |                     </p>
 1407 | |                   </div>
 1408 | |   
 1409 | |                   {/* الأولويات المقترحة */}
 1410 | |                   <Card className="mt-4">
 1411 | |                     <CardHeader>
 1412 | |                       <CardTitle>الأولويات المقترحة للأسبوع القادم</CardTitle>
 1413 | |                       <CardDescription>
 1414 | |                         مهام يجب التركيز عليها في الأسبوع القادم
 1415 | |                       </CardDescription>
 1416 | |                     </CardHeader>
 1417 | |                     <CardContent>
 1418 | |                       <div className="space-y-4">
 1419 | |                         {/* المهام المتأخرة */}
 1420 | |                         {report?.inProgressTasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length ? (
 1421 | |                           <div>
 1422 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1423 | |                               <AlertTriangle className="h-4 w-4 text-red-500 ml-2" />
 1424 | |                               المهام المتأخرة
 1425 | |                             </h4>
 1426 | |                             <ul className="space-y-1 text-sm pr-5">
 1427 | |                               {report.inProgressTasks
 1428 | |                                 .filter(t => t.dueDate && new Date(t.dueDate) < new Date())
 1429 | |                                 .map((task, index) => (
 1430 | |                                   <li key={index} className="list-disc">
 1431 | |                                     {task.description || task.title} - متأخرة منذ {task.dueDate ? Math.ceil((new Date().getTime() - new Date(task.dueDate).getTime()) / (1000 * 60 * 60 * 24)) : 0} أيام
 1432 | |                                   </li>
 1433 | |                                 ))}
 1434 | |                             </ul>
 1435 | |                           </div>
 1436 | |                         ) : null}
 1437 | |   
 1438 | |                         {/* المهام المعلقة */}
 1439 | |                         {report?.blockedTasks?.length ? (
 1440 | |                           <div>
 1441 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1442 | |                               <PauseCircle className="h-4 w-4 text-gray-500 ml-2" />
 1443 | |                               المهام المعلقة
 1444 | |                             </h4>
 1445 | |                             <ul className="space-y-1 text-sm pr-5">
 1446 | |                               {report.blockedTasks
 1447 | |                                 .slice(0, 3)
 1448 | |                                 .map((task, index) => (
 1449 | |                                   <li key={index} className="list-disc">
 1450 | |                                     {task.description || task.title} - تحديد العقبات وإيجاد حلول
 1451 | |                                   </li>
 1452 | |                                 ))}
 1453 | |                               {report.blockedTasks.length > 3 && (
 1454 | |                                 <li className="text-xs text-muted-foreground">
 1455 | |                                   + {report.blockedTasks.length - 3} مهام معلقة أخرى...
 1456 | |                                 </li>
 1457 | |                               )}
 1458 | |                             </ul>
 1459 | |                           </div>
 1460 | |                         ) : null}
 1461 | |   
 1462 | |                         {/* المهام ذات الأولوية العالية */}
 1463 | |                         {report?.upcomingTasks?.filter(t => t.priority === 'high').length ? (
 1464 | |                           <div>
 1465 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1466 | |                               <span className="inline-block w-3 h-3 rounded-full bg-red-500 ml-2"></span>
 1467 | |                               المهام ذات الأولوية العالية
 1468 | |                             </h4>
 1469 | |                             <ul className="space-y-1 text-sm pr-5">
 1470 | |                               {report.upcomingTasks
 1471 | |                                 .filter(t => t.priority === 'high')
 1472 | |                                 .slice(0, 3)
 1473 | |                                 .map((task, index) => (
 1474 | |                                   <li key={index} className="list-disc">
 1475 | |                                     {task.description || task.title}
 1476 | |                                     {task.dueDate ? ` - تاريخ الاستحقاق: ${format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar })}` : ''}
 1477 | |                                   </li>
 1478 | |                                 ))}
 1479 | |                               {report.upcomingTasks.filter(t => t.priority === 'high').length > 3 && (
 1480 | |                                 <li className="text-xs text-muted-foreground">
 1481 | |                                   + {report.upcomingTasks.filter(t => t.priority === 'high').length - 3} مهام أخرى ذات أولوية عالية...
 1482 | |                                 </li>
 1483 | |                               )}
 1484 | |                             </ul>
 1485 | |                           </div>
 1486 | |                         ) : null}
 1487 | |   
 1488 | |                         {/* المهام القريبة من الموعد النهائي */}
 1489 | |                         {report?.upcomingTasks?.filter(t =>
 1490 | |                           t.dueDate &&
 1491 | |                           new Date(t.dueDate) > new Date() &&
 1492 | |                           (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1493 | |                         ).length ? (
 1494 | |                           <div>
 1495 | |                             <h4 className="text-sm font-semibold mb-2 flex items-center">
 1496 | |                               <Clock className="h-4 w-4 text-amber-500 ml-2" />
 1497 | |                               المهام القريبة من الموعد النهائي
 1498 | |                             </h4>
 1499 | |                             <ul className="space-y-1 text-sm pr-5">
 1500 | |                               {report.upcomingTasks
 1501 | |                                 .filter(t =>
 1502 | |                                   t.dueDate &&
 1503 | |                                   new Date(t.dueDate) > new Date() &&
 1504 | |                                   (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1505 | |                                 )
 1506 | |                                 .slice(0, 3)
 1507 | |                                 .map((task, index) => (
 1508 | |                                   <li key={index} className="list-disc">
 1509 | |                                     {task.description || task.title} - متبقي {task.dueDate ? Math.ceil((new Date(task.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0} أيام
 1510 | |                                   </li>
 1511 | |                                 ))}
 1512 | |                               {report.upcomingTasks.filter(t =>
 1513 | |                                 t.dueDate &&
 1514 | |                                 new Date(t.dueDate) > new Date() &&
 1515 | |                                 (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1516 | |                               ).length > 3 && (
 1517 | |                                 <li className="text-xs text-muted-foreground">
 1518 | |                                   + {report.upcomingTasks.filter(t =>
 1519 | |                                     t.dueDate &&
 1520 | |                                     new Date(t.dueDate) > new Date() &&
 1521 | |                                     (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1522 | |                                   ).length - 3} مهام أخرى قريبة من الموعد النهائي...
 1523 | |                                 </li>
 1524 | |                               )}
 1525 | |                             </ul>
 1526 | |                           </div>
 1527 | |                         ) : null}
 1528 | |                       </div>
 1529 | |                     </CardContent>
 1530 | |                   </Card>
 1531 | |   
 1532 | |                   {/* استراتيجيات مقترحة */}
 1533 | |                   <Card className="mt-4">
 1534 | |                     <CardHeader>
 1535 | |                       <CardTitle>استراتيجيات مقترحة لتحسين الإنتاجية</CardTitle>
 1536 | |                     </CardHeader>
 1537 | |                     <CardContent>
 1538 | |                       <div className="space-y-4">
 1539 | |                         {/* استراتيجيات مخصصة بناءً على التحليل */}
 1540 | |                         <div className="space-y-2">
 1541 | |                           {report?.keyMetrics?.completionRate && report.keyMetrics.completionRate < 50 ? (
 1542 | |                             <div className="bg-blue-50 p-3 rounded-md">
 1543 | |                               <h4 className="text-sm font-semibold text-blue-700 mb-2">تحسين معدل إكمال المهام</h4>
 1544 | |                               <ul className="space-y-1 text-sm pr-5">
 1545 | |                                 <li className="list-disc">تقسيم المهام الكبيرة إلى مهام فرعية أصغر يمكن إنجازها بسهولة.</li>
 1546 | |                                 <li className="list-disc">تخصيص وقت محدد يوميًا للعمل على المهام ذات الأولوية العالية.</li>
 1547 | |                                 <li className="list-disc">استخدام تقنية بومودورو (25 دقيقة عمل، 5 دقائق راحة) لزيادة التركيز.</li>
 1548 | |                               </ul>
 1549 | |                             </div>
 1550 | |                           ) : null}
 1551 | |   
 1552 | |                           {report?.keyMetrics?.onTimeCompletionRate && report.keyMetrics.onTimeCompletionRate < 70 ? (
 1553 | |                             <div className="bg-amber-50 p-3 rounded-md">
 1554 | |                               <h4 className="text-sm font-semibold text-amber-700 mb-2">تحسين الالتزام بالمواعيد النهائية</h4>
 1555 | |                               <ul className="space-y-1 text-sm pr-5">
 1556 | |                                 <li className="list-disc">تحديد مواعيد نهائية واقعية للمهام مع إضافة وقت احتياطي.</li>
 1557 | |                                 <li className="list-disc">تقسيم المهام الكبيرة إلى مراحل مع مواعيد نهائية لكل مرحلة.</li>
 1558 | |                                 <li className="list-disc">البدء في المهام مبكرًا وعدم تأجيلها حتى اقتراب الموعد النهائي.</li>
 1559 | |                               </ul>
 1560 | |                             </div>
 1561 | |                           ) : null}
 1562 | |   
 1563 | |                           {report?.blockedTasks?.length ? (
 1564 | |                             <div className="bg-gray-50 p-3 rounded-md">
 1565 | |                               <h4 className="text-sm font-semibold text-gray-700 mb-2">التعامل مع المهام المعلقة</h4>
 1566 | |                               <ul className="space-y-1 text-sm pr-5">
 1567 | |                                 <li className="list-disc">تحديد العقبات التي تعيق كل مهمة بوضوح.</li>
 1568 | |                                 <li className="list-disc">طلب المساعدة أو الموارد اللازمة لتجاوز العقبات.</li>
 1569 | |                                 <li className="list-disc">إعادة تقييم المهام المعلقة لفترة طويلة وتحديد ما إذا كانت لا تزال ضرورية.</li>
 1570 | |                               </ul>
 1571 | |                             </div>
 1572 | |                           ) : null}
 1573 | |   
 1574 | |                           {report?.upcomingTasks?.length > 5 ? (
 1575 | |                             <div className="bg-green-50 p-3 rounded-md">
 1576 | |                               <h4 className="text-sm font-semibold text-green-700 mb-2">إدارة المهام القادمة</h4>
 1577 | |                               <ul className="space-y-1 text-sm pr-5">
 1578 | |                                 <li className="list-disc">ترتيب المهام القادمة حسب الأولوية والموعد النهائي.</li>
 1579 | |                                 <li className="list-disc">تحديد المهام التي يمكن تفويضها أو تأجيلها إذا لزم الأمر.</li>
 1580 | |                                 <li className="list-disc">وضع خطة زمنية واضحة للبدء في المهام القادمة.</li>
 1581 | |                               </ul>
 1582 | |                             </div>
 1583 | |                           ) : null}
 1584 | |                         </div>
 1585 | |                       </div>
 1586 | |                     </CardContent>
 1587 | |                   </Card>
 1588 | |   
 1589 | |                   {/* خطة الأسبوع القادم */}
 1590 | |                   <Card className="mt-4">
 1591 | |                     <CardHeader>
 1592 | |                       <CardTitle>خطة الأسبوع القادم</CardTitle>
 1593 | |                       <CardDescription>
 1594 | |                         توزيع مقترح للمهام على أيام الأسبوع القادم
 1595 | |                       </CardDescription>
 1596 | |                     </CardHeader>
 1597 | |                     <CardContent>
 1598 | |                       <div className="space-y-4">
 1599 | |                         {/* توزيع المهام على أيام الأسبوع */}
 1600 | |                         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
 1601 | |                           {/* اليوم الأول والثاني */}
 1602 | |                           <div className="space-y-3">
 1603 | |                             <div>
 1604 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الأول والثاني</h4>
 1605 | |                               <ul className="space-y-1 text-sm pr-5">
 1606 | |                                 <li className="list-disc">التركيز على المهام المتأخرة والمعلقة.</li>
 1607 | |                                 {report?.inProgressTasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length ? (
 1608 | |                                   <li className="list-disc">إكمال {Math.min(report.inProgressTasks.filter(t => t.dueDate && new Date(t.dueDate) < new Date()).length, 2)} مهام متأخرة.</li>
 1609 | |                                 ) : null}
 1610 | |                                 {report?.blockedTasks?.length ? (
 1611 | |                                   <li className="list-disc">حل العقبات في {Math.min(report.blockedTasks.length, 2)} مهام معلقة.</li>
 1612 | |                                 ) : null}
 1613 | |                                 <li className="list-disc">مراجعة وتحديث خطة العمل الأسبوعية.</li>
 1614 | |                               </ul>
 1615 | |                             </div>
 1616 | |                             <div>
 1617 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الثالث والرابع</h4>
 1618 | |                               <ul className="space-y-1 text-sm pr-5">
 1619 | |                                 <li className="list-disc">التركيز على المهام ذات الأولوية العالية.</li>
 1620 | |                                 {report?.upcomingTasks?.filter(t => t.priority === 'high').length ? (
 1621 | |                                   <li className="list-disc">البدء في {Math.min(report.upcomingTasks.filter(t => t.priority === 'high').length, 2)} مهام ذات أولوية عالية.</li>
 1622 | |                                 ) : null}
 1623 | |                                 {report?.inProgressTasks?.length ? (
 1624 | |                                   <li className="list-disc">متابعة التقدم في المهام قيد التنفيذ.</li>
 1625 | |                                 ) : null}
 1626 | |                                 <li className="list-disc">تحديث نسب التقدم في المهام.</li>
 1627 | |                               </ul>
 1628 | |                             </div>
 1629 | |                           </div>
 1630 | |                           {/* اليوم الخامس والسادس والسابع */}
 1631 | |                           <div className="space-y-3">
 1632 | |                             <div>
 1633 | |                               <h4 className="text-sm font-semibold mb-2">اليوم الخامس والسادس</h4>
 1634 | |                               <ul className="space-y-1 text-sm pr-5">
 1635 | |                                 <li className="list-disc">التركيز على المهام القريبة من الموعد النهائي.</li>
 1636 | |                                 {report?.upcomingTasks?.filter(t =>
 1637 | |                                   t.dueDate &&
 1638 | |                                   new Date(t.dueDate) > new Date() &&
 1639 | |                                   (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1640 | |                                 ).length ? (
 1641 | |                                   <li className="list-disc">العمل على {Math.min(report.upcomingTasks.filter(t =>
 1642 | |                                     t.dueDate &&
 1643 | |                                     new Date(t.dueDate) > new Date() &&
 1644 | |                                     (new Date(t.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7
 1645 | |                                   ).length, 2)} مهام قريبة من الموعد النهائي.</li>
 1646 | |                                 ) : null}
 1647 | |                                 <li className="list-disc">إكمال المهام قيد التنفيذ قدر الإمكان.</li>
 1648 | |                               </ul>
 1649 | |                             </div>
 1650 | |                             <div>
 1651 | |                               <h4 className="text-sm font-semibold mb-2">اليوم السابع</h4>
 1652 | |                               <ul className="space-y-1 text-sm pr-5">
 1653 | |                                 <li className="list-disc">مراجعة ما تم إنجازه خلال الأسبوع.</li>
 1654 | |                                 <li className="list-disc">تحديث حالة جميع المهام.</li>
 1655 | |                                 <li className="list-disc">التخطيط للأسبوع القادم.</li>
 1656 | |                                 <li className="list-disc">تحديد الدروس المستفادة وفرص التحسين.</li>
 1657 | |                               </ul>
 1658 | |                             </div>
 1659 | |                           </div>
 1660 | |                         </div>
 1661 | |                       </div>
 1662 | |                     </CardContent>
 1663 | |                   </Card>
 1664 | |                 </TabsContent>
 1665 | |   
 1666 | |                 <TabsContent value="blocked" className="space-y-4">
 1667 | |                   <div className="flex items-center mb-4">
 1668 | |                     <PauseCircle className="ml-2 h-5 w-5 text-gray-500" />
 1669 | |                     <h3 className="text-lg font-semibold">المهام معلقة</h3>
 1670 | |                     <Badge className="mr-2 bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-300">
 1671 | |                       {report?.blockedTasks?.length || 0}
 1672 | |                     </Badge>
 1673 | |                   </div>
 1674 | |   
 1675 | |                   {!report?.blockedTasks?.length ? (
 1676 | |                     <div className="text-center py-4 space-y-2">
 1677 | |                       <p className="text-muted-foreground">
 1678 | |                         لا توجد مهام معلقة.
 1679 | |                       </p>
 1680 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1681 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتعامل مع المهام معلقة:</h4>
 1682 | |                         <ul className="text-xs text-right space-y-1">
 1683 | |                           <li className="flex items-start">
 1684 | |                             <span className="ml-1 text-primary">•</span>
 1685 | |                             <span>حدد بوضوح سبب تعليق كل مهمة وما هي العقبات التي تواجهها.</span>
 1686 | |                           </li>
 1687 | |                           <li className="flex items-start">
 1688 | |                             <span className="ml-1 text-primary">•</span>
 1689 | |                             <span>ضع خطة عمل لإزالة العقبات وإعادة تنشيط المهام المعلقة.</span>
 1690 | |                           </li>
 1691 | |                           <li className="flex items-start">
 1692 | |                             <span className="ml-1 text-primary">•</span>
 1693 | |                             <span>راجع المهام المعلقة بانتظام لتحديد ما إذا كانت لا تزال ذات صلة أو يجب إلغاؤها.</span>
 1694 | |                           </li>
 1695 | |                         </ul>
 1696 | |                       </div>
 1697 | |                     </div>
 1698 | |                   ) : (
 1699 | |                     <div className="space-y-3">
 1700 | |                       {report.blockedTasks.map((task) => {
 1701 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1702 | |                         const taskForCard = {
 1703 | |                           id: task.id,
 1704 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1705 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1706 | |                           status: 'pending',
 1707 | |                           progress: task.progress || 0,
 1708 | |                           priority: typeof task.priority === 'string' ?
 1709 | |                             task.priority === 'high' ? 'high' :
 1710 | |                             task.priority === 'medium' ? 'medium' :
 1711 | |                             task.priority === 'low' ? 'low' : 'medium'
 1712 | |                             : task.priority as any,
 1713 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1714 | |                         } as TaskType;
 1715 | |   
 1716 | |                         return (
 1717 | |                           <TaskCardTemp
 1718 | |                             key={task.id}
 1719 | |                             id={task.id}
 1720 | |                             task={taskForCard}
 1721 | |                             aiReasoning={task.comment || "هذه المهمة معلقة وتحتاج إلى مراجعة"}
 1722 | |                           />
 1723 | |                         );
 1724 | |                       })}
 1725 | |                     </div>
 1726 | |                   )}
 1727 | |                 </TabsContent>
 1728 | |   
 1729 | |                 <TabsContent value="overdue" className="space-y-4">
 1730 | |                   <div className="flex items-center mb-4">
 1731 | |                     <AlertTriangle className="ml-2 h-5 w-5 text-red-500" />
 1732 | |                     <h3 className="text-lg font-semibold">المهام فائتة</h3>
 1733 | |                     <Badge className="mr-2 bg-red-100 text-red-800 hover:bg-red-200 border-red-300">
 1734 | |                       {report?.overdueTasks?.length || 0}
 1735 | |                     </Badge>
 1736 | |                   </div>
 1737 | |   
 1738 | |                   {!report?.overdueTasks?.length ? (
 1739 | |                     <div className="text-center py-4 space-y-2">
 1740 | |                       <p className="text-muted-foreground">
 1741 | |                         لا توجد مهام فائتة.
 1742 | |                       </p>
 1743 | |                       <div className="bg-muted/20 p-3 rounded-md max-w-md mx-auto">
 1744 | |                         <h4 className="text-sm font-medium mb-2 text-primary">نصائح للتعامل مع المهام الفائتة:</h4>
 1745 | |                         <ul className="text-xs text-right space-y-1">
 1746 | |                           <li className="flex items-start">
 1747 | |                             <span className="ml-1 text-primary">•</span>
 1748 | |                             <span>حدد أولويات المهام الفائتة وابدأ بالأكثر أهمية.</span>
 1749 | |                           </li>
 1750 | |                           <li className="flex items-start">
 1751 | |                             <span className="ml-1 text-primary">•</span>
 1752 | |                             <span>قم بتحديث المواعيد النهائية إذا كانت المهام لا تزال ذات صلة.</span>
 1753 | |                           </li>
 1754 | |                           <li className="flex items-start">
 1755 | |                             <span className="ml-1 text-primary">•</span>
 1756 | |                             <span>حلل أسباب التأخير لتجنب تكرارها في المستقبل.</span>
 1757 | |                           </li>
 1758 | |                         </ul>
 1759 | |                       </div>
 1760 | |                     </div>
 1761 | |                   ) : (
 1762 | |                     <div className="space-y-3">
 1763 | |                       {report.overdueTasks.map((task) => {
 1764 | |                         // تحويل TaskSummary إلى TaskType للتوافق مع TaskCardTemp
 1765 | |                         const taskForCard = {
 1766 | |                           id: task.id,
 1767 | |                           description: task.description || task.title || '', // استخدام title إذا كان description غير موجود
 1768 | |                           details: task.comment || task.notes || '', // استخدام notes إذا كان comment غير موجود
 1769 | |                           status: 'pending',
 1770 | |                           progress: task.progress || 0,
 1771 | |                           priority: typeof task.priority === 'string' ?
 1772 | |                             task.priority === 'high' ? 'high' :
 1773 | |                             task.priority === 'medium' ? 'medium' :
 1774 | |                             task.priority === 'low' ? 'low' : 'medium'
 1775 | |                             : task.priority as any,
 1776 | |                           dueDate: task.dueDate ? new Date(task.dueDate) : undefined
 1777 | |                         } as TaskType;
 1778 | |   
 1779 | |                         return (
 1780 | |                           <TaskCardTemp
 1781 | |                             key={task.id}
 1782 | |                             id={task.id}
 1783 | |                             task={taskForCard}
 1784 | |                             aiReasoning={`مهمة فائتة - تاريخ الاستحقاق: ${task.dueDate ? format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ar }) : 'غير محدد'}`}
 1785 | |                           />
 1786 | |                         );
 1787 | |                       })}
 1788 | |                     </div>
 1789 | |                   )}
 1790 | |                 </TabsContent>
 1791 | |   
 1792 | |                 {/* تبويب الرسوم البيانية */}
 1793 | |                 <TabsContent value="charts" className="space-y-4">
 1794 | |                   <WeeklyReportCharts
 1795 | |                     completedTasks={report?.completedTasks}
 1796 | |                     inProgressTasks={report?.inProgressTasks}
 1797 | |                     upcomingTasks={report?.upcomingTasks}
 1798 | |                     blockedTasks={report?.blockedTasks}
 1799 | |                     overdueTasks={report?.overdueTasks}
 1800 | |                     keyMetrics={report?.keyMetrics}
 1801 | |                   />
 1802 | |                 </TabsContent>
 1803 | |   
 1804 | |                 {/* تبويب تحليل الاتجاهات */}
 1805 | |                 <TabsContent value="trends" className="space-y-4">
 1806 | |                   {weeklyComparison ? (
 1807 | |                     <WeeklyTrendAnalysis comparison={weeklyComparison} />
 1808 | |                   ) : (
 1809 | |                     <div className="text-center py-8">
 1810 | |                       <p className="text-muted-foreground">جاري تحميل بيانات المقارنة...</p>
 1811 | |                     </div>
 1812 | |                   )}
 1813 | |                 </TabsContent>
 1814 | |   
 1815 | |                 {/* تبويب تحليل الأقسام */}
 1816 | |                 <TabsContent value="departments" className="space-y-4">
 1817 | |                   {departmentData.length > 0 ? (
 1818 | |                     <DepartmentAnalysis departments={departmentData} />
 1819 | |                   ) : (
 1820 | |                     <div className="text-center py-8">
 1821 | |                       <p className="text-muted-foreground">لا توجد بيانات أقسام متاحة أو جاري التحميل...</p>
 1822 | |                     </div>
 1823 | |                   )}
 1824 | |                 </TabsContent>
 1825 | |   
 1826 | |                 {/* تبويب التصدير */}
 1827 | |                 <TabsContent value="export" className="space-y-4">
 1828 | |                   <AdvancedExport
 1829 | |                     data={{
 1830 | |                       title: `التقرير الأسبوعي - ${formatDate(reportPeriod.startDate)} إلى ${formatDate(reportPeriod.endDate)}`,
 1831 | |                       summary: report?.summary || '',
 1832 | |                       completedTasks: report?.completedTasks || [],
 1833 | |                       inProgressTasks: report?.inProgressTasks || [],
 1834 | |                       upcomingTasks: report?.upcomingTasks || [],
 1835 | |                       blockedTasks: report?.blockedTasks || [],
 1836 | |                       keyMetrics: report?.keyMetrics || {
 1837 | |                         completionRate: 0,
 1838 | |                         onTimeCompletionRate: 0,
 1839 | |                         averageProgress: 0
 1840 | |                       },
 1841 | |                       recommendations: report?.recommendations || [],
 1842 | |                       departmentData: departmentData
 1843 | |                     }}
 1844 | |                     reportElement={reportElementRef}
 1845 | |                   />
 1846 | |                 </TabsContent>
 1847 | |               </Tabs>
 1848 | |             </>
 1849 | |           )}
 1850 | |         </CardContent>
 1851 | |       </Card>
 1852 | `->   );
 1853 |     }
 1854 |     
      `----
  x Expression expected
      ,-[/workspace/src/components/WeeklyReportCard.tsx:1853:1]
 1850 |       </CardContent>
 1851 |     </Card>
 1852 |   );
 1853 | }
      : ^
 1854 | 
      `----
Caused by:
    Syntax Error
Import trace for requested module:
./src/components/WeeklyReportCard.tsx
./src/app/(app)/reports/weekly/page.tsx
> Build failed because of webpack errors
/layers/google.nodejs.firebasenextjs/npm_modules/node_modules/@apphosting/common/dist/index.js:36
                reject(new Error(`Build process exited with error code ${code}.`));
                       ^
Error: Build process exited with error code 1.
    at ChildProcess.<anonymous> (/layers/google.nodejs.firebasenextjs/npm_modules/node_modules/@apphosting/common/dist/index.js:36:24)
    at ChildProcess.emit (node:events:524:28)
    at ChildProcess._handle.onexit (node:internal/child_process:293:12)
Node.js v20.19.2"}
ERROR: failed to build: exit status 1
ERROR: failed to build: executing lifecycle: failed with status code: 51
Finished Step #3 - "pack"
ERROR
ERROR: build step 3 "gcr.io/k8s-skaffold/pack" failed: step exited with non-zero status: 1

