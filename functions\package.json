{"name": "functions", "description": "Cloud Functions for Firebase - Organizations", "private": true, "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "postinstall": "npm run build"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@google/generative-ai": "^0.2.1", "@sendgrid/mail": "^8.1.5", "@types/cors": "^2.8.17", "@types/node": "^20.17.32", "@types/uuid": "^9.0.8", "cors": "^2.8.5", "date-fns": "^3.3.1", "firebase-admin": "^12.0.0", "firebase-functions": "^4.5.0", "nodemailer": "^6.10.1", "resend": "^4.5.1", "typescript": "^5.8.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/nodemailer": "^6.4.17", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.29.1"}}