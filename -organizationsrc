diff.astextplain.textconv=astextplain
filter.lfs.clean=git-lfs clean -- %f
filter.lfs.smudge=git-lfs smudge -- %f
filter.lfs.process=git-lfs filter-process
filter.lfs.required=true
http.sslbackend=schannel
core.autocrlf=true
core.fscache=true
core.symlinks=false
pull.rebase=false
credential.helper=manager
credential.https://dev.azure.com.usehttppath=true
init.defaultbranch=master
user.email=<EMAIL>
user.name=User
credential.helper=wincred
core.repositoryformatversion=0
core.filemode=false
core.bare=false
core.logallrefupdates=true
core.symlinks=false
core.ignorecase=true
remote.origin.url=https://github.com/trefaeezk/AI-TASKS.git
remote.origin.fetch=+refs/heads/*:refs/remotes/origin/*
branch.main.vscode-merge-base=origin/master
branch.main.remote=origin
branch.main.merge=refs/heads/main
branch.org-firebase-setup.vscode-merge-base=origin/main
