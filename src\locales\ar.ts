
/**
 * ملف اللغة العربية
 * Arabic language file
 */

const ar = {
  // العامة - General
  general: {
    appName: "ذكاء المهام",
    welcome: "مرحباً بك في نظام إدارة المهام",
    loading: "جاري التحميل...",
    saving: "جاري الحفظ...",
    save: "حفظ",
    creating: "جاري الإنشاء...",
    cancel: "إلغاء",
    delete: "حذف",
    edit: "تعديل",
    create: "إنشاء",
    search: "بحث",
    filter: "تصفية",
    filters: "الفلاتر",
    applyFilters: "تطبيق الفلاتر",
    resetFilters: "إعادة تعيين الفلاتر",
    noFiltersAvailable: "لا توجد فلاتر متاحة لهذه الصفحة",
    noResults: "لا توجد نتائج",
    confirm: "تأكيد",
    back: "رجوع",
    next: "التالي",
    submit: "إرسال",
    success: "تم بنجاح",
    error: "حدث خطأ",
    refresh: "تحديث",
    required: "مطلوب",
    optional: "اختياري",
    more: "المزيد",
    less: "أقل",
    all: "الكل",
    add: "إضافة",
    adding: "جاري الإضافة...",
    updating: "جاري التحديث...",
    deleting: "جاري الحذف...",
    saveChanges: "حفظ التغييرات",
    none: "لا شيء",
    today: "اليوم",
    yesterday: "أمس",
    tomorrow: "غداً",
    now: "الآن",
    date: "التاريخ",
    time: "الوقت",
    datetime: "التاريخ والوقت",
    day: "يوم",
    week: "أسبوع",
    month: "شهر",
    year: "سنة",
    name: "الاسم",
    description: "الوصف",
    status: "الحالة",
    type: "النوع",
    category: "الفئة",
    categories: "الفئات",
    tags: "الوسوم",
    priority: "الأولوية",
    dueDate: "تاريخ الاستحقاق",
    createdAt: "تاريخ الإنشاء",
    updatedAt: "تاريخ التحديث",
    assignedTo: "مسند إلى",
    createdBy: "أنشئ بواسطة",
    updatedBy: "حُدّث بواسطة",
    settings: "الإعدادات",
    logout: "تسجيل الخروج",
    login: "تسجيل الدخول",
    register: "تسجيل",
    profile: "الملف الشخصي",
    dashboard: "لوحة التحكم",
    help: "المساعدة",
    about: "حول",
    contact: "اتصل بنا",
    language: "اللغة",
    changeLanguage: "تغيير اللغة",
    theme: "السمة",
    dark: "داكن",
    light: "فاتح",
    system: "النظام",
    direction: "rtl",
    menu: "فتح/إغلاق الشريط الجانبي",
    general: "عام",
    features: "الميزات",
    errorLoadingPageContextOrUser: "خطأ في تحميل سياق الصفحة أو المستخدم."
  },

  sidebar: {
    mainMenu: "القائمة الرئيسية",
    dashboard: "لوحة التحكم", // For organization dashboard
    tasks: "المهام",
    tasksTooltip: "عرض وإدارة المهام",
    dailyPlan: "الخطة اليومية",
    dailyPlanTooltip: "عرض خطة المهام اليومية المقترحة",
    reports: "التقارير", // Generic reports for individual and org
    weeklyReports: "التقارير الأسبوعية",
    weeklyReportsTooltip: "عرض التقارير الأسبوعية",
    meetings: "الاجتماعات",
    meetingsTooltip: "عرض وإدارة اجتماعات المؤسسة",
    kpi: "مؤشرات الأداء",
    kpiTooltip: "عرض مؤشرات الأداء الرئيسية",
    okr: "التخطيط السنوي (OKRs)",
    okrTooltip: "إدارة الأهداف والنتائج الرئيسية للمؤسسة",
    tools: "الأدوات",
    toolsTooltip: "أدوات مساعدة",
    smartSuggestions: "الاقتراحات الذكية",
    smartSuggestionsTooltip: "عرض الاقتراحات الذكية",
    adminMenu: "إدارة النظام",
    adminPanel: "لوحة الإدارة",
    adminPanelTooltip: "الوصول إلى لوحة تحكم المسؤول",
    users: "المستخدمين", // For admin section
    usersTooltip: "إدارة المستخدمين والصلاحيات",
    members: "الأعضاء", // For organization members
    membersTooltip: "إدارة أعضاء المؤسسة",
    departments: "الأقسام",
    departmentsTooltip: "إدارة أقسام المؤسسة",
    organizationSettings: "إعدادات المؤسسة",
    organizationSettingsTooltip: "إدارة إعدادات المؤسسة",
    dataManagement: "إدارة البيانات", // Generic data management
    dataManagementTooltip: "إدارة بيانات النظام (تصدير/استيراد)",
    importExport: "استيراد/تصدير البيانات", // For individual user data
    importExportTooltip: "استيراد وتصدير بيانات المستخدم الفردي",
    organizationDataManagement: "إدارة بيانات المؤسسة",
    organizationDataManagementTooltip: "تصدير واستيراد بيانات المؤسسة",
    organizationRequests: "طلبات المؤسسات",
    organizationRequestsTooltip: "مراجعة طلبات إنشاء المؤسسات",
    generalSection: "عام",
    settings: "الإعدادات", // General settings for individual user
    settingsTooltip: "تعديل إعدادات التطبيق",
    documentation: "الوثائق",
    documentationTooltip: "عرض وثائق النظام",
    diagnostics: "التشخيص",
    diagnosticsTooltip: "أدوات التشخيص (للمالك فقط)",
    userInfoTitle: "معلومات المستخدم",
    currentUserDefault: "المستخدم الحالي",
    userRoleLoading: "جار التحميل...",
    signOut: "تسجيل الخروج",
    signOutTooltip: "تسجيل الخروج من الحساب",
    toggleSidebar: "تبديل الشريط الجانبي"
  },

  roles: {
    // أدوار النظام العامة (النمط الجديد is* فقط)
    isSystemOwner: "مالك النظام",
    isSystemAdmin: "أدمن النظام العام",
    isIndependent: "مستخدم مستقل",

    // أدوار المؤسسات
    isOrgOwner: "مالك المؤسسة",
    isOrgAdmin: "أدمن المؤسسة",
    isOrgSupervisor: "مشرف",
    isOrgEngineer: "مهندس",
    isOrgTechnician: "فني",
    isOrgAssistant: "مساعد فني"
  },

  roleDescriptions: {
    // أدوار النظام العامة (النمط الجديد is* فقط)
    isSystemOwner: "مالك النظام - أعلى صلاحية في النظام بالكامل، يدير جميع الأنظمة والمؤسسات",
    isSystemAdmin: "أدمن النظام العام - صلاحيات واسعة لإدارة النظام والمؤسسات",
    isIndependent: "مستخدم مستقل - يدير مهامه وبياناته الشخصية فقط",

    // أدوار المؤسسات
    isOrgOwner: "مالك المؤسسة - صلاحيات كاملة داخل المؤسسة",
    isOrgAdmin: "أدمن المؤسسة - صلاحيات إدارية واسعة داخل المؤسسة",
    isOrgSupervisor: "مشرف - يشرف على الفرق والمهام ويدير العمليات",
    isOrgEngineer: "مهندس - يصمم ويخطط المشاريع والحلول التقنية",
    isOrgTechnician: "فني - ينفذ المهام التقنية والصيانة",
    isOrgAssistant: "مساعد فني - يساعد في تنفيذ المهام البسيطة"
  },

  common: {
    applyFilters: "تطبيق الفلاتر",
    filters: "الفلاتر",
    resetFilters: "إعادة تعيين الفلاتر",
    noFiltersAvailable: "لا توجد فلاتر متاحة لهذه الصفحة"
  },

  // المصادقة - Authentication
  auth: {
    login: "تسجيل الدخول",
    register: "إنشاء حساب",
    logout: "تسجيل الخروج",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    confirmPassword: "تأكيد كلمة المرور",
    forgotPassword: "نسيت كلمة المرور؟",
    resetPassword: "إعادة تعيين كلمة المرور",
    changePassword: "تغيير كلمة المرور",
    currentPassword: "كلمة المرور الحالية",
    newPassword: "كلمة المرور الجديدة",
    confirmNewPassword: "تأكيد كلمة المرور الجديدة",
    rememberMe: "تذكرني",
    loginWith: "تسجيل الدخول باستخدام",
    registerWith: "إنشاء حساب باستخدام",
    alreadyHaveAccount: "لديك حساب بالفعل؟",
    dontHaveAccount: "ليس لديك حساب؟",
    noAccount: "ليس لديك حساب؟",
    createAccount: "أنشئ حسابًا",
    verifyEmail: "تحقق من بريدك الإلكتروني",
    verificationCode: "رمز التحقق",
    resendCode: "إعادة إرسال الرمز",
    verificationEmailSent: "تم إرسال رسالة التحقق إلى بريدك الإلكتروني",
    passwordResetEmailSent: "تم إرسال رسالة إعادة تعيين كلمة المرور إلى بريدك الإلكتروني",
    passwordChanged: "تم تغيير كلمة المرور بنجاح",
    invalidCredentials: "بريد إلكتروني أو كلمة مرور غير صحيحة",
    accountCreated: "تم إنشاء الحساب بنجاح",
    accountNotFound: "الحساب غير موجود",
    accountDisabled: "الحساب معطل",
    accountLocked: "الحساب مقفل",
    tooManyAttempts: "محاولات كثيرة جدًا، حاول مرة أخرى لاحقًا",
    sessionExpired: "انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى",
    logoutConfirmation: "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    logoutSuccess: "تم تسجيل الخروج بنجاح",
    loginDescription: "مرحبًا بعودتك! قم بتسجيل الدخول للوصول إلى مهامك.",
    registerDescription: "انضم إلينا لإدارة مهامك بذكاء.",
    namePlaceholder: "الاسم الكامل",
    nameRequired: "الاسم مطلوب",
    emailPlaceholder: "البريد الإلكتروني",
    passwordPlaceholder: "كلمة المرور",
    confirmPasswordPlaceholder: "تأكيد كلمة المرور",
    confirmPasswordRequired: "تأكيد كلمة المرور مطلوب",
    passwordsDoNotMatch: "كلمتا المرور غير متطابقتين",
    invalidEmail: "البريد الإلكتروني غير صالح",
    passwordMinLength: "كلمة المرور يجب أن تكون على الأقل {length} أحرف",
    loginFailed: "فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك.",
    signupFailed: "فشل إنشاء الحساب. قد يكون البريد الإلكتروني مستخدمًا بالفعل.",
    resetPasswordFailed: "فشل إرسال رابط إعادة تعيين كلمة المرور.",
    resetPasswordDescription: "أدخل بريدك الإلكتروني لتلقي رابط إعادة تعيين كلمة المرور.",
    backToLogin: "العودة إلى تسجيل الدخول",
    sendResetLink: "إرسال رابط إعادة التعيين",
    googleSignInFailed: "فشل تسجيل الدخول باستخدام جوجل.",
    orContinueWith: "أو استمر بواسطة",
    signInWithGoogle: "تسجيل الدخول باستخدام جوجل",
    registrationSuccessful: "تم التسجيل بنجاح!",
    accountCreatedSuccessfully: "تم إنشاء حسابك بنجاح",
    pleaseSignIn: "يرجى تسجيل الدخول للمتابعة",
    redirectingToLogin: "جاري التوجيه إلى صفحة تسجيل الدخول...",
    goToLogin: "الذهاب إلى تسجيل الدخول",
  },

  // الاقتراحات - Suggestions
  suggestions: {
    smartSuggestions: "الاقتراحات الذكية",
    errorLoadingSuggestions: "حدث خطأ أثناء تحميل الاقتراحات",
    errorLoadingTasks: "حدث خطأ أثناء تحميل المهام",
    noSuggestions: "لا توجد اقتراحات",
    generateNewSuggestion: "قم بتوليد اقتراح جديد باستخدام الأزرار أدناه. الاقتراحات تعتمد على مهامك الحالية.",
    currentUserTasks: "مهام المستخدم الحالية ({count})",
    tasksDescription: "عرض لمهام المستخدم التي سيتم استخدامها في توليد الاقتراحات.",
    noTasks: "لا توجد مهام",
    noTasksDescription: "لم يتم العثور على أي مهام للمستخدم الحالي. يجب إنشاء مهام أولاً قبل توليد الاقتراحات.",
    showHideTasksList: "عرض/إخفاء قائمة المهام ({count})",
    refreshTasksList: "تحديث قائمة المهام",
    showLastAiResponse: "عرض استجابة JSON الأخيرة من الذكاء الاصطناعي (للمالك فقط)",
    jsonResponse: "استجابة JSON",
  },

  // المهام - Tasks
  tasks: {
    tasks: "المهام",
    task: "مهمة",
    myTasks: "مهامي",
    assignedTasks: "المهام المسندة",
    allTasks: "جميع المهام",
    newTask: "مهمة جديدة",
    editTask: "تعديل المهمة",
    deleteTask: "حذف المهمة",
    deleteTaskConfirmation: "هل أنت متأكد أنك تريد حذف هذه المهمة؟",
    deleteTaskWarning: "هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف مهمتك بشكل دائم.",
    taskDeleted: "تم حذف المهمة بنجاح",
    taskDeletedSuccessfully: "تم حذف المهمة بنجاح.",
    taskDeleteError: "خطأ في حذف المهمة",
    errorDeletingTask: "حدث خطأ أثناء محاولة حذف المهمة.",
    taskCreated: "تم إنشاء المهمة بنجاح",
    taskUpdated: "تم تحديث المهمة بنجاح",
    taskDetails: "تفاصيل المهمة",
    taskName: "اسم المهمة",
    taskDescription: "وصف المهمة",
    taskStatus: "حالة المهمة",
    taskPriority: "أولوية المهمة",
    taskDueDate: "تاريخ استحقاق المهمة",
    taskCategory: "فئة المهمة",
    taskAssignee: "المسؤول عن المهمة",
    taskCreator: "منشئ المهمة",
    taskCreatedAt: "تاريخ إنشاء المهمة",
    taskUpdatedAt: "تاريخ تحديث المهمة",
    taskComments: "تعليقات المهمة",
    taskAttachments: "مرفقات المهمة",
    taskTags: "علامات المهمة",
    taskSubtasks: "المهام الفرعية",
    taskParent: "المهمة الرئيسية",
    taskDependencies: "اعتماديات المهمة",
    taskBlocking: "المهام المعتمدة",
    taskBlockedBy: "المهام المعتمد عليها",
    taskEstimatedTime: "الوقت المقدر",
    taskActualTime: "الوقت الفعلي",
    taskTimeSpent: "الوقت المستغرق",
    taskTimeRemaining: "الوقت المتبقي",
    taskProgress: "تقدم المهمة",
    taskNotes: "ملاحظات المهمة",
    taskHistory: "سجل المهمة",
    taskActivities: "أنشطة المهمة",
    taskWatchers: "المراقبون",
    addWatcher: "إضافة مراقب",
    removeWatcher: "إزالة مراقب",
    watchTask: "مراقبة المهمة",
    unwatchTask: "إلغاء مراقبة المهمة",
    assignToMe: "إسناد لي",
    unassign: "إلغاء الإسناد",
    markAsComplete: "تعليم كمكتملة",
    markAsIncomplete: "تعليم كغير مكتملة",
    overdue: "متأخرة",
    dueToday: "مستحقة اليوم",
    dueSoon: "مستحقة قريبًا",
    noDueDate: "بدون تاريخ استحقاق",
    highPriority: "أولوية عالية",
    mediumPriority: "أولوية متوسطة",
    lowPriority: "أولوية منخفضة",
    noPriority: "بدون أولوية",
    statusNew: "جديدة",
    statusInProgress: "قيد التنفيذ",
    statusOnHold: "معلقة",
    statusCompleted: "مكتملة",
    statusCancelled: "ملغاة",
    filterByStatus: "تصفية حسب الحالة",
    filterByPriority: "تصفية حسب الأولوية",
    filterByAssignee: "تصفية حسب المسؤول",
    filterByDueDate: "تصفية حسب تاريخ الاستحقاق",
    filterByCategory: "تصفية حسب الفئة",
    sortByDueDate: "ترتيب حسب تاريخ الاستحقاق",
    sortByPriority: "ترتيب حسب الأولوية",
    sortByStatus: "ترتيب حسب الحالة",
    sortByName: "ترتيب حسب الاسم",
    sortByCreatedAt: "ترتيب حسب تاريخ الإنشاء",
    sortByUpdatedAt: "ترتيب حسب تاريخ التحديث",
    noTasks: "لا توجد مهام",
    noTasksFound: "لم يتم العثور على مهام",
    noTasksCreated: "لم يتم إنشاء مهام بعد",
    createFirstTask: "إنشاء أول مهمة",
    noTasksToDisplay: "لا توجد مهام لعرضها.",
    useButtonToAddNewTask: "استخدم زر {button} في الأعلى لإضافة مهمة جديدة!",
    noTasksMatchCurrentFilters: "لا توجد مهام تطابق الفلاتر الحالية.",
    removeFilters: "إزالة الفلاتر",
    noTasksInCategory: "لا توجد مهام في هذه الفئة {category} {dateRange}.",
    forCategory: "للفئة \"{category}\"",
    withinSpecifiedDateRange: "ضمن نطاق التاريخ المحدد",
    cannotUpdateStatus: "لا يمكن تحديث الحالة.",
    statusUpdated: "تم تحديث الحالة",
    taskStatusUpdatedTo: "تم تحديث حالة المهمة إلى {status}.",
    statusUpdateError: "خطأ في تحديث الحالة",
    errorUpdatingTaskStatus: "حدث خطأ أثناء تحديث حالة المهمة.",
    invalidTaskIdOrNotLoggedIn: "معرف المهمة غير صالح أو لم يتم تسجيل الدخول.",
    taskMoved: "تم نقل المهمة",
    taskMovedTo: "تم نقل المهمة إلى {category}.",
    taskMoveError: "خطأ في نقل المهمة",
    errorMovingTask: "حدث خطأ أثناء نقل المهمة.",
    category: {
      overdue: "فائتة",
      today: "اليوم",
      upcoming: "قادمة",
      scheduled: "مجدولة",
      pending: "معلقة",
      hold: "متوقفة",
      completed: "مكتملة"
    }
  },

  // التقارير - Reports
  reports: {
    reports: "التقارير",
    report: "تقرير",
    dailyPlan: "الخطة اليومية",
    weeklyReport: "التقرير الأسبوعي",
    monthlyReport: "التقرير الشهري",
    customReport: "تقرير مخصص",
    generateReport: "إنشاء تقرير",
    reportGenerated: "تم إنشاء التقرير بنجاح",
    reportPeriod: "فترة التقرير",
    reportType: "نوع التقرير",
    reportFormat: "تنسيق التقرير",
    reportData: "بيانات التقرير",
    reportFilters: "مرشحات التقرير",
    reportSummary: "ملخص التقرير",
    reportDetails: "تفاصيل التقرير",
    reportCharts: "رسوم بيانية للتقرير",
    reportTables: "جداول التقرير",
    selectPeriod: "اختر الفترة",
    selectReportType: "اختر نوع التقرير",
    quarter: "ربع سنة",
    kpiType: "نوع المؤشر",
    selectKpiType: "اختر نوع المؤشر",
    allKpis: "جميع المؤشرات",
    completionRate: "معدل الإنجاز",
    byPriority: "حسب الأولوية",
    exportReport: "تصدير التقرير",
    printReport: "طباعة التقرير",
    shareReport: "مشاركة التقرير",
    saveReport: "حفظ التقرير",
    savedReports: "التقارير المحفوظة",
    noReports: "لا توجد تقارير",
    noReportsFound: "لم يتم العثور على تقارير",
    noReportsCreated: "لم يتم إنشاء تقارير بعد",
    createFirstReport: "إنشاء أول تقرير",
    tasksCompleted: "المهام المكتملة",
    tasksInProgress: "المهام قيد التنفيذ",
    tasksOverdue: "المهام المتأخرة",
    tasksDueToday: "المهام المستحقة اليوم",
    tasksDueSoon: "المهام المستحقة قريبًا",
    tasksCreated: "المهام المنشأة",
    tasksAssigned: "المهام المسندة",
    tasksUpdated: "المهام المحدثة",
    timeSpent: "الوقت المستغرق",
    timeRemaining: "الوقت المتبقي",
    productivity: "الإنتاجية",
    efficiency: "الكفاءة",
    performance: "الأداء",
    progress: "التقدم",
    trends: "الاتجاهات",
    comparisons: "المقارنات",
    statistics: "الإحصائيات",
    metrics: "المقاييس",
    kpis: "مؤشرات الأداء الرئيسية",
  },

  // الإشعارات - Notifications
  notifications: {
    notifications: "الإشعارات",
    notificationsTooltip: "عرض الإشعارات",
    notification: "إشعار",
    notificationSettings: "إعدادات الإشعارات",
    notificationSettingsDescription: "إدارة إعدادات الإشعارات وتفضيلات التنبيهات",
    customizeNotifications: "تخصيص كيفية استلام الإشعارات وأنواعها",
    manageNotifications: "إدارة الإشعارات",
    markAllAsRead: "تعليم الكل كمقروء",
    markAsRead: "تعليم كمقروء",
    markAsUnread: "تعليم كغير مقروء",
    deleteNotification: "حذف الإشعار",
    deleteAllNotifications: "حذف جميع الإشعارات",
    notificationDeleted: "تم حذف الإشعار بنجاح",
    allNotificationsDeleted: "تم حذف جميع الإشعارات بنجاح",
    allNotificationsMarkedAsRead: "تم تعليم جميع الإشعارات كمقروءة",
    noNotifications: "لا توجد إشعارات",
    noNotificationsFound: "لم يتم العثور على إشعارات",
    noUnreadNotifications: "لا توجد إشعارات غير مقروءة",
    enableNotifications: "تمكين الإشعارات",
    disableNotifications: "تعطيل الإشعارات",
    notificationsEnabled: "تم تمكين الإشعارات",
    notificationsDisabled: "تم تعطيل الإشعارات",
    pushNotifications: "إشعارات الدفع",
    emailNotifications: "إشعارات البريد الإلكتروني",
    inAppNotifications: "إشعارات داخل التطبيق",
    receiveInAppNotifications: "استلام إشعارات داخل التطبيق",
    receiveEmailNotifications: "استلام إشعارات عبر البريد الإلكتروني",
    receiveAiSuggestions: "استلام اقتراحات ذكية لتحسين الإنتاجية",
    selectEmailFrequency: "اختر تكرار البريد الإلكتروني",
    notifyOnTaskAssigned: "إشعار عند إسناد مهمة",
    notifyOnTaskUpdated: "إشعار عند تحديث مهمة",
    notifyOnTaskCompleted: "إشعار عند اكتمال مهمة",
    notifyOnTaskOverdue: "إشعار عند تأخر مهمة",
    notifyOnTaskComment: "إشعار عند التعليق على مهمة",
    notifyOnMention: "إشعار عند الإشارة إليك",
    notifyOnSystemUpdates: "إشعار عند تحديثات النظام",
    notificationFrequency: "تكرار الإشعارات",
    immediate: "فوري",
    hourly: "كل ساعة",
    daily: "يومي",
    weekly: "أسبوعي",
    never: "أبدًا",
    doNotDisturb: "عدم الإزعاج",
    doNotDisturbDescription: "تعيين فترة زمنية لا ترغب في استلام إشعارات خلالها",
    enableDoNotDisturb: "تفعيل وقت عدم الإزعاج",
    doNotDisturbFrom: "من",
    doNotDisturbTo: "إلى",
    unread: "غير مقروء",
    read: "مقروء",
    all: "الكل",
    taskNotifications: "إشعارات المهام",
    taskNotificationsDescription: "إنشاء المهام، تعيين المهام، تغيير الحالة",
    systemNotifications: "إشعارات النظام",
    systemNotificationsDescription: "تحديثات النظام، إضافة أعضاء، إنشاء أقسام",
    meetingNotifications: "إشعارات الاجتماعات",
    meetingNotificationsDescription: "إنشاء الاجتماعات، تذكيرات، تحديثات",
    aiSuggestions: "اقتراحات الذكاء الاصطناعي",
    notificationTypes: "أنواع الإشعارات",
    chooseNotificationTypes: "اختر أنواع الإشعارات التي ترغب في استلامها",
    generalSettings: "إعدادات عامة",
    pleaseLoginToView: "يرجى تسجيل الدخول لعرض إعدادات الإشعارات.",
    settingsSaved: "تم حفظ إعدادات الإشعارات بنجاح",
    errorLoadingSettings: "حدث خطأ أثناء تحميل إعدادات الإشعارات",
    errorSavingSettings: "حدث خطأ أثناء حفظ إعدادات الإشعارات",
  },

  // المؤسسة - Organization
  organization: {
    organization: "المؤسسة",
    organizations: "المؤسسات",
    organizationSettings: "إعدادات المؤسسة",
    organizationMembers: "أعضاء المؤسسة",
    organizationDepartments: "أقسام المؤسسة",
    organizationRoles: "أدوار المؤسسة",
    organizationPermissions: "صلاحيات المؤسسة",
    organizationInvitations: "دعوات المؤسسة",
    organizationRequests: "طلبات المؤسسة",
    organizationDataNotFound: "لم يتم العثور على بيانات المؤسسة",
    errorFetchingOrganizationSettings: "حدث خطأ أثناء جلب إعدادات المؤسسة",
    organizationSettingsSaved: "تم حفظ إعدادات المؤسسة بنجاح",
    errorSavingOrganizationSettings: "حدث خطأ أثناء حفظ إعدادات المؤسسة",
    basicInformation: "المعلومات الأساسية",
    basicInformationDescription: "المعلومات الأساسية عن المؤسسة",
    permissionsSettings: "إعدادات الصلاحيات",
    permissionsSettingsDescription: "تحكم في صلاحيات أعضاء المؤسسة",
    allowMemberInvites: "السماح بدعوة أعضاء جدد",
    allowMemberInvitesDescription: "السماح للمشرفين بدعوة أعضاء جدد للمؤسسة",
    allowDepartmentCreation: "السماح بإنشاء أقسام جديدة",
    allowDepartmentCreationDescription: "السماح للمشرفين بإنشاء أقسام جديدة في المؤسسة",
    requireTaskApproval: "طلب موافقة على المهام",
    requireTaskApprovalDescription: "طلب موافقة المشرف على المهام الجديدة قبل نشرها",
    advancedFeaturesDescription: "تفعيل أو تعطيل الميزات المتقدمة في النظام",
    aiFeatures: "ميزات الذكاء الاصطناعي",
    aiFeaturesDescription: "تفعيل ميزات الذكاء الاصطناعي مثل اقتراح المهام وتوليد التقارير",
    createOrganization: "إنشاء مؤسسة",
    editOrganization: "تعديل المؤسسة",
    deleteOrganization: "حذف المؤسسة",
    leaveOrganization: "مغادرة المؤسسة",
    joinOrganization: "الانضمام إلى مؤسسة",
    switchOrganization: "تبديل المؤسسة",
    organizationName: "اسم المؤسسة",
    organizationDescription: "وصف المؤسسة",
    organizationLogo: "شعار المؤسسة",
    organizationWebsite: "موقع المؤسسة",
    organizationEmail: "بريد المؤسسة الإلكتروني",
    organizationPhone: "هاتف المؤسسة",
    organizationAddress: "عنوان المؤسسة",
    organizationCreatedAt: "تاريخ إنشاء المؤسسة",
    organizationUpdatedAt: "تاريخ تحديث المؤسسة",
    organizationOwner: "مالك المؤسسة",
    organizationAdmin: "مدير المؤسسة",
    organizationMember: "عضو المؤسسة",
    organizationGuest: "ضيف المؤسسة",
    addMember: "إضافة عضو",
    removeMember: "إزالة عضو",
    inviteMember: "دعوة عضو",
    acceptInvitation: "قبول الدعوة",
    rejectInvitation: "رفض الدعوة",
    cancelInvitation: "إلغاء الدعوة",
    resendInvitation: "إعادة إرسال الدعوة",
    memberAdded: "تمت إضافة العضو بنجاح",
    memberRemoved: "تمت إزالة العضو بنجاح",
    invitationSent: "تم إرسال الدعوة بنجاح",
    invitationAccepted: "تم قبول الدعوة بنجاح",
    invitationRejected: "تم رفض الدعوة بنجاح",
    invitationCancelled: "تم إلغاء الدعوة بنجاح",
    invitationResent: "تم إعادة إرسال الدعوة بنجاح",
    noOrganizations: "لا توجد مؤسسات",
    noOrganizationsFound: "لم يتم العثور على مؤسسات",
    noOrganizationsCreated: "لم يتم إنشاء مؤسسات بعد",
    createFirstOrganization: "إنشاء أول مؤسسة",
    noMembers: "لا يوجد أعضاء",
    noMembersFound: "لم يتم العثور على أعضاء",
    noInvitations: "لا توجد دعوات",
    noInvitationsFound: "لم يتم العثور على دعوات",
    noDepartments: "لا توجد أقسام",
    noDepartmentsFound: "لم يتم العثور على أقسام",
    createDepartment: "إنشاء قسم",
    editDepartment: "تعديل القسم",
    deleteDepartment: "حذف القسم",
    departmentName: "اسم القسم",
    departmentDescription: "وصف القسم",
    departmentHead: "رئيس القسم",
    departmentMembers: "أعضاء القسم",
    addToDepartment: "إضافة إلى القسم",
    removeFromDepartment: "إزالة من القسم",
    departmentCreated: "تم إنشاء القسم بنجاح",
    departmentUpdated: "تم تحديث القسم بنجاح",
    departmentDeleted: "تم حذف القسم بنجاح",
    memberAddedToDepartment: "تمت إضافة العضو إلى القسم بنجاح",
    memberRemovedFromDepartment: "تمت إزالة العضو من القسم بنجاح",

    // 🏢 الأقسام - Departments
    departments: "الأقسام",
    department: "قسم",
    departmentsManagement: "إدارة الأقسام",
    createNewDepartment: "إنشاء قسم جديد",
    departmentDetails: "تفاصيل القسم",
    departmentStatistics: "إحصائيات القسم",
    departmentTasks: "مهام القسم",
    departmentMeetings: "اجتماعات القسم",
    noDepartmentDescription: "لا يوجد وصف للقسم",
    departmentCreatedSuccessfully: "تم إنشاء القسم بنجاح",
    errorCreatingDepartment: "حدث خطأ أثناء إنشاء القسم",
    errorFetchingDepartments: "حدث خطأ أثناء جلب الأقسام",

    // 👥 الأفراد - Individuals
    individuals: "الأفراد",
    individual: "فرد",
    individualsInOrganization: "الأفراد في المؤسسة",
    membersWithoutDepartment: "أعضاء بدون قسم",
    unassignedMembers: "أعضاء غير معينين",
    assignToDepartment: "تعيين إلى قسم",
    noIndividuals: "لا يوجد أفراد",
    noIndividualsInOrganization: "لا يوجد أفراد في المؤسسة",
    allMembersAssigned: "جميع الأعضاء معينون إلى أقسام",

    // 📊 مفاتيح إضافية للأعضاء والأقسام
    member: "عضو",
    departmentFormDescription: "أدخل معلومات القسم الجديد. اضغط على حفظ عند الانتهاء.",
    canAssignToDepartment: "يمكن تعيين هؤلاء الأعضاء إلى أقسام",
    noDepartmentMembers: "لا يوجد أعضاء في الأقسام",
    assignMembersToDepartments: "قم بتعيين الأعضاء إلى أقسام",
    membersAssignedToDepartments: "الأعضاء المعينون إلى أقسام المؤسسة",
    unknownDepartment: "قسم غير معروف",
    unassigned: "غير معين",
    addMemberDescription: "أدخل بريد العضو الإلكتروني ودوره في المؤسسة",
    selectRole: "اختر الدور",
    selectDepartment: "اختر القسم",
    noDepartment: "بدون قسم",
    editMember: "تعديل بيانات العضو",
    editMemberDescription: "تعديل دور العضو وقسمه في المؤسسة",
    confirmDeleteMember: "هل أنت متأكد من حذف هذا العضو؟",
    deleteMemberWarning: "سيتم إزالة العضو من المؤسسة. هذا الإجراء لا يمكن التراجع عنه",
    noOrganizationAccess: "لا يمكن الوصول إلى المؤسسة",
    noOrganizationAccessDescription: "يجب أن تكون عضواً في مؤسسة للوصول إلى هذه الصفحة",
    goToOrganization: "اذهب إلى المؤسسة",
  },

  // الإعدادات - Settings
  settings: {
    settings: "الإعدادات",
    generalSettings: "الإعدادات العامة",
    accountSettings: "إعدادات الحساب",
    profileSettings: "إعدادات الملف الشخصي",
    securitySettings: "إعدادات الأمان",
    notificationSettings: "إعدادات الإشعارات",
    appearanceSettings: "إعدادات المظهر",
    languageSettings: "إعدادات اللغة",
    themeSettings: "إعدادات السمة",
    privacySettings: "إعدادات الخصوصية",
    advancedSettings: "إعدادات متقدمة",
    experimentalSettings: "إعدادات تجريبية",
    developerSettings: "إعدادات المطور",
    systemSettings: "إعدادات النظام",
    saveSettings: "حفظ الإعدادات",
    resetSettings: "إعادة تعيين الإعدادات",
    exportSettings: "تصدير الإعدادات",
    importSettings: "استيراد الإعدادات",
    features: "الميزات",
    section: "القسم",
    selectSection: "اختر القسم",
    settingsSaved: "تم حفظ الإعدادات بنجاح",
    settingsReset: "تم إعادة تعيين الإعدادات بنجاح",
    settingsExported: "تم تصدير الإعدادات بنجاح",
    settingsImported: "تم استيراد الإعدادات بنجاح",
    appearance: "المظهر",
    language: "اللغة",
    theme: "السمة",
    darkMode: "الوضع الداكن",
    lightMode: "الوضع الفاتح",
    systemMode: "وضع النظام",
    fontSize: "حجم الخط",
    fontFamily: "نوع الخط",
    colorScheme: "نظام الألوان",
    dateFormat: "تنسيق التاريخ",
    timeFormat: "تنسيق الوقت",
    timezone: "المنطقة الزمنية",
    firstDayOfWeek: "اليوم الأول من الأسبوع",
    currency: "العملة",
    measurementUnit: "وحدة القياس",
    notifications: "الإشعارات",
    privacy: "الخصوصية",
    security: "الأمان",
    twoFactorAuth: "المصادقة الثنائية",
    changePassword: "تغيير كلمة المرور",
    deleteAccount: "حذف الحساب",
    dataExport: "تصدير البيانات",
    dataImport: "استيراد البيانات",
    dataDeletion: "حذف البيانات",
    cookies: "ملفات تعريف الارتباط",
    termsOfService: "شروط الخدمة",
    privacyPolicy: "سياسة الخصوصية",
    about: "حول",
    help: "المساعدة",
    feedback: "التعليقات",
    contactSupport: "الاتصال بالدعم",
    reportBug: "الإبلاغ عن خطأ",
    requestFeature: "طلب ميزة",
    version: "الإصدار",
    buildNumber: "رقم البناء",
    releaseDate: "تاريخ الإصدار",
    licenseInfo: "معلومات الترخيص",
    thirdPartyLibraries: "مكتبات الطرف الثالث",
    acknowledgements: "شكر وتقدير",
  },

  // الأدوات - Tools
  tools: {
    tools: "الأدوات",
    calculator: "الحاسبة",
    calendar: "التقويم",
    notes: "الملاحظات",
    timer: "المؤقت",
    stopwatch: "ساعة التوقف",
    alarm: "المنبه",
    reminders: "التذكيرات",
    weather: "الطقس",
    maps: "الخرائط",
    translator: "المترجم",
    converter: "المحول",
    scanner: "الماسح الضوئي",
    recorder: "المسجل",
    camera: "الكاميرا",
    aiTools: "أدوات الذكاء الاصطناعي",
    aiSuggestions: "الاقتراحات الذكية",
    aiTaskSuggestions: "اقتراحات المهام الذكية",
    aiPrioritization: "ترتيب الأولويات الذكي",
    aiScheduling: "جدولة ذكية",
    aiAnalytics: "تحليلات ذكية",
    aiAssistant: "المساعد الذكي",
    aiChatbot: "روبوت المحادثة الذكي",
    aiSummary: "ملخص ذكي",
    aiTranslation: "ترجمة ذكية",
    aiVoiceAssistant: "مساعد صوتي ذكي",
    aiImageGeneration: "توليد صور ذكي",
    aiTextGeneration: "توليد نصوص ذكي",
    aiCodeGeneration: "توليد كود ذكي",
    aiDataAnalysis: "تحليل بيانات ذكي",
    aiPrediction: "تنبؤ ذكي",
    aiRecommendation: "توصيات ذكية",
    aiPersonalization: "تخصيص ذكي",
    aiOptimization: "تحسين ذكي",
    aiAutomation: "أتمتة ذكية",
    underDevelopment: "قيد التطوير",
    aiPrompts: "برومبت الذكاء الاصطناعي",
    promptTitle: "عنوان البرومبت",
    promptDescription: "وصف البرومبت",
    promptContent: "محتوى البرومبت",
    promptCategory: "فئة البرومبت",
    promptTags: "علامات البرومبت",
    promptCreator: "منشئ البرومبت",
    promptCreatedAt: "تاريخ إنشاء البرومبت",
    promptUpdatedAt: "تاريخ تحديث البرومبت",
    promptUsageCount: "عدد استخدامات البرومبت",
    promptRating: "تقييم البرومبت",
    promptFeedback: "تعليقات البرومبت",
    promptShared: "مشاركة البرومبت",
    promptPrivate: "برومبت خاص",
    promptPublic: "برومبت عام",
    createPrompt: "إنشاء برومبت",
    editPrompt: "تعديل البرومبت",
    deletePrompt: "حذف البرومبت",
    usePrompt: "استخدام البرومبت",
    sharePrompt: "مشاركة البرومبت",
    savePrompt: "حفظ البرومبت",
    copyPrompt: "نسخ البرومبت",
    favoritePrompt: "إضافة البرومبت للمفضلة",
    unfavoritePrompt: "إزالة البرومبت من المفضلة",
    promptDeleted: "تم حذف البرومبت بنجاح",
    promptCreated: "تم إنشاء البرومبت بنجاح",
    promptUpdated: "تم تحديث البرومبت بنجاح",
    promptCopied: "تم نسخ البرومبت بنجاح",
    promptSharedSuccess: "تم مشاركة البرومبت بنجاح",
    promptSaved: "تم حفظ البرومبت بنجاح",
    noPrompts: "لا توجد برومبتات",
    noPromptsFound: "لم يتم العثور على برومبتات",
    noPromptsCreated: "لم يتم إنشاء برومبتات بعد",
    createFirstPrompt: "إنشاء أول برومبت",
    myPrompts: "برومبتاتي",
    sharedPrompts: "البرومبتات المشتركة",
    publicPrompts: "البرومبتات العامة",
    favoritePrompts: "البرومبتات المفضلة",
    recentPrompts: "البرومبتات الأخيرة",
    popularPrompts: "البرومبتات الشائعة",
    promptLibrary: "مكتبة البرومبتات",
    importPrompts: "استيراد برومبتات",
    exportPrompts: "تصدير برومبتات",
    searchPrompts: "البحث في البرومبتات",
    filterPromptsByCategory: "تصفية البرومبتات حسب الفئة",
    sortPromptsByDate: "ترتيب البرومبتات حسب التاريخ",
    sortPromptsByRating: "ترتيب البرومبتات حسب التقييم",
    sortPromptsByUsage: "ترتيب البرومبتات حسب الاستخدام",
  },

  // الاجتماعات - Meetings
  meetings: {
    meetings: "الاجتماعات",
    meeting: "اجتماع",
    createMeeting: "إنشاء اجتماع",
    editMeeting: "تعديل الاجتماع",
    deleteMeeting: "حذف الاجتماع",
    meetingTitle: "عنوان الاجتماع",
    meetingDescription: "وصف الاجتماع",
    meetingDate: "تاريخ الاجتماع",
    meetingTime: "وقت الاجتماع",
    meetingLocation: "موقع الاجتماع",
    meetingParticipants: "مشاركي الاجتماع",
    meetingAgenda: "جدول أعمال الاجتماع",
    meetingNotes: "ملاحظات الاجتماع",
    meetingDecisions: "قرارات الاجتماع",
    meetingTasks: "مهام الاجتماع",
    meetingStatus: "حالة الاجتماع",
    meetingType: "نوع الاجتماع",
    noMeetings: "لا توجد اجتماعات",
    upcomingMeetings: "الاجتماعات القادمة",
    pastMeetings: "الاجتماعات السابقة",
    todayMeetings: "اجتماعات اليوم",
  },

  // إدارة البيانات - Data Management
  dataManagement: {
    dataManagement: "إدارة البيانات",
    importData: "استيراد البيانات",
    exportData: "تصدير البيانات",
    backupData: "نسخ احتياطي للبيانات",
    restoreData: "استعادة البيانات",
    deleteData: "حذف البيانات",
    dataImported: "تم استيراد البيانات بنجاح",
    dataExported: "تم تصدير البيانات بنجاح",
    dataBackedUp: "تم عمل نسخة احتياطية للبيانات بنجاح",
    dataRestored: "تم استعادة البيانات بنجاح",
    dataDeleted: "تم حذف البيانات بنجاح",
    importFromFile: "استيراد من ملف",
    exportToFile: "تصدير إلى ملف",
    selectFileToImport: "اختر ملف للاستيراد",
    selectDataToExport: "اختر البيانات للتصدير",
    confirmDataDeletion: "تأكيد حذف البيانات",
    dataImportError: "خطأ في استيراد البيانات",
    dataExportError: "خطأ في تصدير البيانات",
    dataBackupError: "خطأ في عمل نسخة احتياطية للبيانات",
    dataRestoreError: "خطأ في استعادة البيانات",
    dataDeleteError: "خطأ في حذف البيانات",
    dataType: "نوع البيانات",
    selectDataType: "اختر نوع البيانات",
  },

  // الأخطاء - Errors
  errors: {
    error: "خطأ",
    warning: "تحذير",
    info: "معلومات",
    success: "نجاح",
    somethingWentWrong: "حدث خطأ ما",
    pageNotFound: "الصفحة غير موجودة",
    accessDenied: "تم رفض الوصول",
    unauthorized: "غير مصرح",
    forbidden: "محظور",
    notFound: "غير موجود",
    badRequest: "طلب غير صالح",
    serverError: "خطأ في الخادم",
    networkError: "خطأ في الشبكة",
    connectionError: "خطأ في الاتصال",
    timeoutError: "انتهت مهلة الاتصال",
    validationError: "خطأ في التحقق",
    authenticationError: "خطأ في المصادقة",
    authorizationError: "خطأ في التفويض",
    databaseError: "خطأ في قاعدة البيانات",
    fileError: "خطأ في الملف",
    uploadError: "خطأ في الرفع",
    downloadError: "خطأ في التنزيل",
    importError: "خطأ في الاستيراد",
    exportError: "خطأ في التصدير",
    printError: "خطأ في الطباعة",
    parseError: "خطأ في التحليل",
    formatError: "خطأ في التنسيق",
    syntaxError: "خطأ في بناء الجملة",
    typeError: "خطأ في النوع",
    rangeError: "خطأ في النطاق",
    referenceError: "خطأ في المرجع",
    internalError: "خطأ داخلي",
    unknownError: "خطأ غير معروف",
    tryAgain: "حاول مرة أخرى",
    refresh: "تحديث",
    goBack: "العودة",
    goHome: "الذهاب إلى الصفحة الرئيسية",
    contactAdmin: "الاتصال بالمسؤول",
    seeDetails: "عرض التفاصيل",
    hideDetails: "إخفاء التفاصيل",
    errorCode: "رمز الخطأ",
    errorMessage: "رسالة الخطأ",
    errorDetails: "تفاصيل الخطأ",
    errorStack: "مكدس الخطأ",
    errorTime: "وقت الخطأ",
    errorLocation: "موقع الخطأ",
    errorUser: "مستخدم الخطأ",
    errorSession: "جلسة الخطأ",
    errorBrowser: "متصفح الخطأ",
    errorOS: "نظام تشغيل الخطأ",
    errorDevice: "جهاز الخطأ",
    errorIP: "عنوان IP الخطأ",
    errorURL: "عنوان URL الخطأ",
    errorMethod: "طريقة الخطأ",
    errorStatus: "حالة الخطأ",
    errorResponse: "استجابة الخطأ",
    errorRequest: "طلب الخطأ",
    errorHeaders: "رؤوس الخطأ",
    errorBody: "جسم الخطأ",
    errorQuery: "استعلام الخطأ",
    errorParams: "معلمات الخطأ",
    errorData: "بيانات الخطأ",
  },
};

export default ar;
