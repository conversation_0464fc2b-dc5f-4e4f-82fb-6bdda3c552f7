rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ===== دوال مساعدة =====
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserToken() {
      return request.auth.token;
    }

    // التحقق من مالك النظام (النمط الموحد is* فقط)
    function isSystemOwner() {
      return isAuthenticated() && getUserToken().isSystemOwner == true;
    }

    // التحقق من أدمن النظام (النمط الموحد is* فقط)
    function isSystemAdmin() {
      return isAuthenticated() && getUserToken().isSystemAdmin == true;
    }

    // التحقق من مالك المؤسسة (النمط الموحد is* فقط)
    function isOrgOwner() {
      return isAuthenticated() && getUserToken().isOrgOwner == true;
    }

    // التحقق من أدوار المؤسسات (النمط الموحد is* فقط)
    function isOrgAdmin() {
      return isAuthenticated() && getUserToken().isOrgAdmin == true;
    }

    function isOrgSupervisor() {
      return isAuthenticated() && getUserToken().isOrgSupervisor == true;
    }

    function isOrgEngineer() {
      return isAuthenticated() && getUserToken().isOrgEngineer == true;
    }

    function isOrgTechnician() {
      return isAuthenticated() && getUserToken().isOrgTechnician == true;
    }

    function isOrgAssistant() {
      return isAuthenticated() && getUserToken().isOrgAssistant == true;
    }

    // التحقق من صلاحيات النظام العليا
    function hasSystemAccess() {
      return isSystemOwner() || isSystemAdmin();
    }

    // التحقق من صلاحيات إدارية عامة (النظام الموحد)
    function hasAdminAccess() {
      return hasSystemAccess() || isOrgOwner() ||
             isOrgAdmin() || isOrgSupervisor() || isOrgEngineer() ||
             isOrgTechnician() || isOrgAssistant();
    }

    // ===== الصلاحيات الديناميكية (بدون تخزين) =====

    // التحقق من صلاحية إدارة النظام
    function canManageSystem() {
      return isSystemOwner();
    }

    // التحقق من صلاحية إدارة المستخدمين
    function canManageUsers() {
      return hasSystemAccess();
    }

    // التحقق من صلاحية إدارة المؤسسات
    function canManageOrganization() {
      return hasSystemAccess() || isOrgOwner();
    }

    // التحقق من صلاحية إدارة المشاريع
    function canManageProjects() {
      return hasSystemAccess() || isOrgOwner() || isOrgAdmin() || isOrgEngineer();
    }

    // التحقق من صلاحية عرض التقارير
    function canViewReports() {
      return hasSystemAccess() || isOrgOwner() ||
             isOrgAdmin() || isOrgSupervisor() || isOrgEngineer();
    }

    // التحقق من صلاحية إنشاء المهام
    function canCreateTasks() {
      return hasSystemAccess() || isOrgOwner() ||
             isOrgAdmin() || isOrgSupervisor() || isOrgEngineer() || isOrgTechnician() ||
             isIndependent() || // المستخدمون المستقلون يمكنهم إنشاء المهام
             isIndividualAccount(); // أصحاب الحسابات الفردية يمكنهم إنشاء المهام
    }

    // التحقق من المستخدم المستقل (النمط الموحد is* فقط)
    function isIndependent() {
      return isAuthenticated() && getUserToken().isIndependent == true;
    }

    // التحقق من نوع الحساب الفردي
    function isIndividualAccount() {
      return isAuthenticated() && getUserToken().accountType == 'individual';
    }

    // التحقق من المستخدم الفردي (دور مستقل أو نوع حساب فردي)
    function isIndividualUser() {
      return isIndependent() || isIndividualAccount() ||
             (isAuthenticated() && getUserToken().role == 'independent');
    }

    // التحقق من المستخدم العادي (أي مستخدم مسجل دخول)
    function isRegularUser() {
      return isAuthenticated() && (
        isIndividualUser() ||
        hasAdminAccess() ||
        getUserToken().role != null ||
        // السماح للمستخدمين الجدد بدون token claims
        (isAuthenticated() && getUserToken().role == null)
      );
    }

    // قاعدة مؤقتة للمستخدمين الجدد
    function isNewUser() {
      return isAuthenticated() && (
        getUserToken().role == null ||
        getUserToken().accountType == null
      );
    }

    // التحقق من الصلاحيات المخصصة
    function hasCustomPermission(permission) {
      return isAuthenticated() &&
        getUserToken().customPermissions != null &&
        getUserToken().customPermissions is list &&
        permission in getUserToken().customPermissions;
    }

    // التحقق من أن التحديث يشمل نقاط التتبع والحالة فقط (للأدوار الأقل)
    function onlyMilestonesAndStatusChanged() {
      return request.resource.data.diff(resource.data).changedKeys().hasOnly(['milestones', 'status', 'updatedAt']);
    }

    // التحقق من الأدوار العليا (يمكنها تعديل كامل)
    function hasHighLevelAccess() {
      return hasSystemAccess() || isOrgOwner() || isOrgAdmin() || isOrgEngineer();
    }

    // ===== قواعد فئات المهام =====
    match /taskCategories/{categoryId} {
      // السماح للجميع بالقراءة
      allow read: if isAuthenticated();

      // المستخدمون المستقلون لهم حرية كاملة في فئاتهم
      allow create, update, delete: if isAuthenticated() && (
        hasAdminAccess() ||
        isIndependent() ||  // المستخدمون المستقلون لهم حرية كاملة
        isIndividualAccount() ||  // أصحاب الحسابات الفردية
        canCreateTasks() ||
        resource.data.createdBy == request.auth.uid ||
        request.resource.data.createdBy == request.auth.uid
      );
    }

    // ===== قواعد المستخدمين (النمط الموحد) =====
    match /users/{userId} {
      // قراءة البيانات الشخصية
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        canManageUsers() ||
        hasCustomPermission('users:view') ||
        hasAdminAccess()
      );

      // إنشاء وثيقة المستخدم الأولى - للمستخدمين الجدد فقط
      allow create: if isAuthenticated() &&
        request.auth.uid == userId &&
        // التأكد من أن هذه وثيقة جديدة وليس تحديث
        !exists(/databases/$(database)/documents/users/$(userId));

      // تحديث البيانات الشخصية (مع منع تعديل الأدوار الحساسة)
      allow update: if isAuthenticated() && (
        (request.auth.uid == userId && (
          // منع المستخدم من تعديل أدواره الحساسة بنفسه
          !('isSystemOwner' in request.resource.data) &&
          !('isSystemAdmin' in request.resource.data) &&
          !('isOrgOwner' in request.resource.data) &&
          !('isOrgAdmin' in request.resource.data) &&
          !('role' in request.resource.data)
        )) ||
        canManageUsers() ||
        hasCustomPermission('users:edit') ||
        hasAdminAccess()
      );

      // حذف المستخدمين - للأدمن فقط
      allow delete: if canManageUsers() || hasAdminAccess();
    }

    // ===== قواعد المهام (النمط الموحد) =====
    match /tasks/{taskId} {
      // قراءة المهام
      allow read: if isAuthenticated() && (
        hasCustomPermission('tasks:view') ||
        canCreateTasks() ||
        hasAdminAccess() ||
        resource.data.createdBy == request.auth.uid ||
        resource.data.assignedTo == request.auth.uid ||
        // للمهام الفردية
        (resource.data.organizationId == null && resource.data.userId == request.auth.uid) ||
        // للمهام المؤسسية - التحقق من العضوية
        (resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId)
      );

      // إنشاء المهام
      allow create: if isAuthenticated() && (
        hasCustomPermission('tasks:create') ||
        canCreateTasks() ||
        hasAdminAccess() ||
        // التأكد من أن المستخدم ينشئ مهمة لنفسه أو لمؤسسته
        (request.resource.data.organizationId == null && request.resource.data.userId == request.auth.uid) ||
        (request.resource.data.organizationId != null && getUserToken().organizationId == request.resource.data.organizationId) ||
        // السماح بإنشاء مهام تتطلب موافقة
        (request.resource.data.status == 'pending-approval' && request.resource.data.requiresApproval == true &&
         request.resource.data.organizationId != null && getUserToken().organizationId == request.resource.data.organizationId)
      );

      // تعديل المهام
      allow update: if isAuthenticated() && (
        hasCustomPermission('tasks:edit') ||
        hasAdminAccess() ||
        resource.data.createdBy == request.auth.uid ||
        // للمهام الفردية
        (resource.data.organizationId == null && resource.data.userId == request.auth.uid) ||
        // للمهام المؤسسية - الأدوار العليا لها صلاحية كاملة
        (resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId && hasHighLevelAccess()) ||
        // موافقة المهام المعلقة - مسئولي الأقسام والمؤسسة
        (resource.data.status == 'pending-approval' && resource.data.requiresApproval == true &&
         resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId &&
         ((resource.data.approvalLevel == 'department' && (isOrgSupervisor() || isOrgEngineer() || hasHighLevelAccess()) &&
           getUserToken().departmentId == resource.data.departmentId) ||
          (resource.data.approvalLevel == 'organization' && (isOrgOwner() || isOrgAdmin())))) ||
        // للمهام المؤسسية - المهندس يمكنه تحديث المهام المُسندة إليه أو مهام قسمه بحرية كاملة
        (resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId &&
         isOrgEngineer() && (
           (resource.data.assignedToUserId == request.auth.uid ||
            (resource.data.assignedToUserIds != null && request.auth.uid in resource.data.assignedToUserIds)) ||
           (resource.data.departmentId != null && getUserToken().departmentId == resource.data.departmentId)
         )) ||
        // للمهام المؤسسية - المشرف يمكنه تحديث مهام قسمه بحرية كاملة
        (resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId &&
         isOrgSupervisor() && (
           (resource.data.assignedToUserId == request.auth.uid ||
            (resource.data.assignedToUserIds != null && request.auth.uid in resource.data.assignedToUserIds)) ||
           (resource.data.departmentId != null && getUserToken().departmentId == resource.data.departmentId)
         )) ||
        // للمهام المؤسسية - الأدوار الأقل يمكنها تحديث نقاط التتبع والحالة فقط للمهام المُسندة إليهم
        (resource.data.organizationId != null && getUserToken().organizationId == resource.data.organizationId &&
         (resource.data.assignedToUserId == request.auth.uid ||
          (resource.data.assignedToUserIds != null && request.auth.uid in resource.data.assignedToUserIds)) &&
         // السماح بتحديث نقاط التتبع والحالة فقط للأدوار الأقل (فني، مساعد)
         (isOrgTechnician() || isOrgAssistant()) &&
         onlyMilestonesAndStatusChanged())
      );

      // حذف المهام
      allow delete: if isAuthenticated() && (
        hasCustomPermission('tasks:delete') ||
        hasAdminAccess() ||
        resource.data.createdBy == request.auth.uid ||
        // للمهام الفردية
        (resource.data.organizationId == null && resource.data.userId == request.auth.uid)
      );
    }

    // ===== قواعد المؤسسات (النمط الموحد) =====
    match /organizations/{orgId} {
      allow read: if isAuthenticated() && (
        canManageOrganization() ||
        hasSystemAccess() ||
        getUserToken().organizationId == orgId
      );

      allow write: if canManageOrganization() || hasSystemAccess() || isOrgOwner();
    }

    // ===== إعدادات النظام (النمط الموحد) =====
    match /system/{document=**} {
      allow read: if isAuthenticated() && (
        hasCustomPermission('settings:view') ||
        hasSystemAccess()
      );
      allow write: if canManageSystem() || hasSystemAccess();
    }

    // ===== قواعد خاصة للحسابات الفردية (النمط الموحد) =====
    match /individualData/{userId} {
      // أصحاب الحسابات الفردية يمكنهم الوصول لبياناتهم الشخصية
      allow read, write: if isAuthenticated() && (
        request.auth.uid == userId && isIndividualAccount()
      );
    }

    // ===== قواعد طلبات المؤسسات (النمط الموحد) =====
    match /organizationRequests/{requestId} {
      allow read: if isAuthenticated() && (
        hasSystemAccess() ||
        resource.data.userId == request.auth.uid
      );

      allow create: if isAuthenticated() && isIndependent();

      allow update: if hasSystemAccess();

      allow delete: if hasSystemAccess();
    }

    // ===== قواعد دعوات المؤسسات (النمط الموحد) =====
    match /organizationInvitations/{invitationId} {
      allow read: if isAuthenticated() && (
        hasSystemAccess() ||
        isOrgOwner() ||
        resource.data.invitedUserId == request.auth.uid
      );

      allow create: if isAuthenticated() && (
        hasSystemAccess() || isOrgOwner()
      );

      allow update: if isAuthenticated() && (
        hasSystemAccess() ||
        isOrgOwner() ||
        resource.data.invitedUserId == request.auth.uid
      );

      allow delete: if hasSystemAccess() || isOrgOwner();
    }

    // ===== قواعد الإشعارات =====
    match /notifications/{notificationId} {
      // قراءة الإشعارات - المستخدم يمكنه قراءة إشعاراته فقط
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        hasSystemAccess() ||
        hasAdminAccess()
      );

      // إنشاء الإشعارات - النظام والأدمن فقط
      allow create: if isAuthenticated() && (
        hasSystemAccess() ||
        hasAdminAccess()
      );

      // تحديث الإشعارات - المستخدم يمكنه تحديث إشعاراته (مثل تعليمها كمقروءة)
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        hasSystemAccess() ||
        hasAdminAccess()
      );

      // حذف الإشعارات - المستخدم يمكنه حذف إشعاراته
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }

    // ===== قواعد إعدادات الإشعارات =====
    match /notificationSettings/{userId} {
      // قراءة إعدادات الإشعارات - المستخدم يمكنه قراءة إعداداته فقط
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        hasSystemAccess() ||
        hasAdminAccess()
      );

      // كتابة إعدادات الإشعارات - المستخدم يمكنه تعديل إعداداته
      allow write: if isAuthenticated() && (
        request.auth.uid == userId ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }



    // ===== قواعد خاصة للمستخدمين المستقلين - حرية كاملة =====
    match /userReports/{userId}/{document=**} {
      // المستخدمون المستقلون لهم حرية كاملة في تقاريرهم
      allow read, write: if isAuthenticated() && (
        request.auth.uid == userId && (isIndependent() || isIndividualAccount()) ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }

    match /userSettings/{userId}/{document=**} {
      // المستخدمون المستقلون لهم حرية كاملة في إعداداتهم
      allow read, write: if isAuthenticated() && (
        request.auth.uid == userId && (isIndependent() || isIndividualAccount()) ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }

    match /userCategories/{userId}/{document=**} {
      // المستخدمون المستقلون لهم حرية كاملة في فئاتهم المخصصة
      allow read, write: if isAuthenticated() && (
        request.auth.uid == userId && (isIndependent() || isIndividualAccount()) ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }

    match /userTemplates/{userId}/{document=**} {
      // المستخدمون المستقلون لهم حرية كاملة في قوالبهم
      allow read, write: if isAuthenticated() && (
        request.auth.uid == userId && (isIndependent() || isIndividualAccount()) ||
        hasSystemAccess() ||
        hasAdminAccess()
      );
    }

    // ===== قواعد عامة للمجموعات الأخرى (النمط الموحد) =====
    match /{document=**} {
      // المستخدمون المستقلون لهم حرية في بياناتهم الشخصية
      allow read: if isAuthenticated() && (
        hasSystemAccess() ||
        hasAdminAccess() ||
        (isIndependent() || isIndividualAccount())  // المستخدمون المستقلون يمكنهم القراءة
      );

      // الكتابة محدودة للأدمن والمستخدمين المستقلين لبياناتهم
      allow write: if hasSystemAccess() || hasAdminAccess();
    }
  }
}
