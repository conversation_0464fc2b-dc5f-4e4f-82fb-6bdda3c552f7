# 📚 دليل التوثيق الشامل - نظام إدارة المهام

## 🎯 نظرة عامة

مرحباً بك في التوثيق الشامل لنظام إدارة المهام. هذا النظام يوفر حلول متكاملة لإدارة المستخدمين والمؤسسات والمهام مع نظام صلاحيات متقدم.

---

## 📋 فهرس التوثيق

### 🔐 نظام المستخدمين والصلاحيات
- **[📚 توثيق نظام المستخدمين](./USER_SYSTEM_DOCUMENTATION.md)**
  - هيكلة الأدوار والصلاحيات
  - أنواع الحسابات (فردية ومؤسسية)
  - الصلاحيات التفصيلية لكل دور
  - هيكل قاعدة البيانات
  - أمثلة عملية

- **[🔐 دليل الصلاحيات والأمان](./USER_PERMISSIONS_GUIDE.md)**
  - نظام الصلاحيات المتقدم
  - التحقق من الصلاحيات (Frontend & Backend)
  - الصلاحيات المخصصة
  - أمان النظام وقواعد الحماية
  - أمثلة برمجية وأكواد جاهزة

### 🏢 إدارة المؤسسات
- **[🏢 دليل إدارة المؤسسات](./ORGANIZATION_MANAGEMENT.md)**
  - إنشاء وإدارة المؤسسات
  - إدارة الأعضاء والأدوار
  - الأقسام والهيكل التنظيمي
  - سير العمل والعمليات
  - أمثلة عملية لشركات مختلفة

### 🔌 التكامل والـ APIs
- **[🔌 مرجع API](./API_REFERENCE.md)**
  - جميع APIs المتاحة
  - المصادقة والتفويض
  - أمثلة التكامل بلغات مختلفة
  - معالجة الأخطاء
  - أكواد جاهزة للاستخدام

---

## 🚀 البدء السريع

### 1. **فهم النظام**
ابدأ بقراءة [توثيق نظام المستخدمين](./USER_SYSTEM_DOCUMENTATION.md) لفهم الهيكل العام والأدوار المختلفة.

### 2. **إعداد المؤسسة**
اتبع دليل [إدارة المؤسسات](./ORGANIZATION_MANAGEMENT.md) لإنشاء مؤسستك وتنظيم الفرق.

### 3. **التكامل التقني**
استخدم [مرجع API](./API_REFERENCE.md) لتطوير التطبيقات والتكامل مع النظام.

### 4. **إدارة الصلاحيات**
راجع [دليل الصلاحيات](./USER_PERMISSIONS_GUIDE.md) لضبط الأمان والتحكم في الوصول.

---

## 🎭 الأدوار والصلاحيات - ملخص سريع

### 🌐 أدوار النظام العامة (النمط الجديد is* فقط)
| الدور | الوصف | الاستخدام |
|-------|--------|-----------|
| `isSystemOwner` | مالك النظام | إدارة النظام بالكامل |
| `isSystemAdmin` | أدمن النظام | إدارة المؤسسات والمستخدمين |
| `isIndependent` | مستخدم مستقل | إدارة المهام الشخصية |

### 🏢 أدوار المؤسسات (النمط الجديد is* فقط)
| الدور | الوصف | الصلاحيات الرئيسية |
|-------|--------|-------------------|
| `isOrgOwner` | مالك المؤسسة | جميع الصلاحيات داخل المؤسسة |
| `isOrgAdmin` | أدمن المؤسسة | إدارة الأعضاء والمهام والتقارير |
| `isOrgSupervisor` | مشرف | إدارة المهام والموافقة عليها |
| `isOrgEngineer` | مهندس | إنشاء وتطوير المشاريع |
| `isOrgTechnician` | فني | تنفيذ المهام التقنية |
| `isOrgAssistant` | مساعد فني | المساعدة في المهام البسيطة |

---

## 🔧 أمثلة سريعة

### 👤 إنشاء مستخدم جديد
```typescript
const userData = {
  email: "<EMAIL>",
  password: "securePassword",
  name: "أحمد محمد",
  role: "org_engineer",
  accountType: "organization",
  organizationId: "org_123"
};

const newUser = await createUser(userData);
```

### 🏢 إنشاء مؤسسة
```typescript
const orgData = {
  name: "شركة التقنية المتقدمة",
  description: "شركة متخصصة في تطوير البرمجيات",
  settings: {
    allowPublicJoin: false,
    requireApproval: true,
    maxMembers: 100
  }
};

const organization = await createOrganization(orgData);
```

### ✅ التحقق من الصلاحيات
```typescript
// Frontend
const { hasPermission } = usePermissions();
const canCreateTasks = hasPermission('tasks:create');

// Backend
const hasAccess = await hasPermission(userId, 'tasks:create');
```

---

## 📞 معلومات الاتصال

### 🌐 الروابط المهمة
- **الموقع الرسمي**: https://yourapp.com
- **التوثيق التقني**: https://docs.yourapp.com
- **حالة النظام**: https://status.yourapp.com

---

*آخر تحديث: ديسمبر 2024*

**نصيحة**: ابدأ بقراءة [توثيق نظام المستخدمين](./USER_SYSTEM_DOCUMENTATION.md) للحصول على فهم شامل للنظام! 🚀
