# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment variables (حماية المعلومات السرية)
.env
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production.local
functions/.env
functions/.env.local
functions/.env.development
functions/.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# firebase functions
functions/node_modules
functions/lib
firebase-debug.log
firebase-debug.*.log
*.log

# IDX specific files
.idx/

