'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  FileText,
  CheckCircle2,
  Clock,
  Calendar,
  AlertTriangle,
  PauseCircle,
  BarChart,
  Download,
  Printer,
  Share2,
  Loader2,
  ListChecks,
  TrendingUp,
  Building
} from 'lucide-react';
import { db } from '@/config/firebase';
import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import {
  generateWeeklyReport,
  type GenerateWeeklyReportInput,
  type GenerateWeeklyReportOutput,
  type Task as TaskInput,
  type TaskSummary
} from '@/services/ai';
import { TaskType, TaskFirestoreData } from '@/types/task';
import { format, startOfWeek, endOfWeek, subWeeks, isWithinInterval, isBefore, isAfter } from 'date-fns';
import { ar } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { TaskCardTemp } from '@/components/TaskCardTemp';
import {
  getWeeklyComparison,
  getEnhancedDepartmentPerformance,
  type WeeklyComparison,
  type DepartmentPerformance
} from '@/services/organizationReports';
import { WeeklyReportCharts } from '@/components/charts/WeeklyReportCharts';
import { WeeklyTrendAnalysis } from '@/components/charts/WeeklyTrendAnalysis';
import { DepartmentAnalysis } from '@/components/charts/DepartmentAnalysis';
import { AdvancedExport } from '@/components/export/AdvancedExport';

interface WeeklyReportCardProps {
  organizationId?: string;
  departmentId?: string;
  userId?: string;
  className?: string;
  reportPeriod?: {
    startDate: Date;
    endDate: Date;
  };
}

export function WeeklyReportCard({ organizationId, departmentId, userId, className, reportPeriod: propReportPeriod }: WeeklyReportCardProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  // الحالات الأساسية
  const [tasks, setTasks] = useState<TaskType[]>([]);
  const [report, setReport] = useState<GenerateWeeklyReportOutput | null>(null);
  const [isLoadingTasks, setIsLoadingTasks] = useState(true);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportGenerated, setReportGenerated] = useState(false);
  const [activeTab, setActiveTab] = useState<'summary' | 'analysis' | 'planning' | 'completed' | 'inProgress' | 'upcoming' | 'overdue' | 'charts' | 'trends' | 'departments' | 'export'>('summary');
  const [error, setError] = useState<string | null>(null);

  // بيانات التحليلات المتقدمة
  const [weeklyComparison, setWeeklyComparison] = useState<WeeklyComparison | null>(null);
  const [departmentData, setDepartmentData] = useState<DepartmentPerformance[]>([]);
  const reportElementRef = useRef<HTMLDivElement>(null);