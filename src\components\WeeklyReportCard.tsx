'use client';

import { PeriodReportCard } from '@/components/PeriodReportCard';

interface WeeklyReportCardProps {
  organizationId?: string;
  departmentId?: string;
  userId?: string;
  className?: string;
  reportPeriod?: {
    startDate: Date;
    endDate: Date;
  };
}

export function WeeklyReportCard({ organizationId, departmentId, userId, className, reportPeriod: propReportPeriod }: WeeklyReportCardProps) {
  // استخدام المكون الجديد الموحد مع تحديد النوع كأسبوعي
  return (
    <PeriodReportCard
      organizationId={organizationId}
      departmentId={departmentId}
      userId={userId}
      className={className}
      defaultPeriodType="weekly"
      reportPeriod={propReportPeriod}
    />
  );
}