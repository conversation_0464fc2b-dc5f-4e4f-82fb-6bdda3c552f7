# إعدادات التطبيق الأساسية
# للتطوير المحلي: http://localhost:9003
# للشبكة المحلية: http://*************:9003
# للإنتاج: https://your-domain.com
NEXT_PUBLIC_APP_BASE_URL=http://*************:9003

# Firebase Configuration (Frontend)
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Gemini AI API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Firebase Admin SDK (Backend) - اختياري
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour_Private_Key_Here\n-----END PRIVATE KEY-----"

# ملاحظة: يمكنك استخدام Application Default Credentials بدلاً من المفاتيح أعلاه
# تأكد من تسجيل الدخول: firebase login
