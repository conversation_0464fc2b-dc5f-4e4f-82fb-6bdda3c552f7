{"hosting": {"source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**/*.@(js|css|html|json|webmanifest)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "env": {"NEXT_PUBLIC_FIREBASE_API_KEY": "AIzaSyBIJrQX5HBBnP7LKzgsUNdWCX7aqhVG3wA", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "tasks-intelligence.firebaseapp.com", "NEXT_PUBLIC_FIREBASE_PROJECT_ID": "tasks-intelligence", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": "tasks-intelligence.appspot.com", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "770714758504", "NEXT_PUBLIC_FIREBASE_APP_ID": "1:770714758504:web:aea98ba39a726df1ba3add"}, "frameworksBackend": {"region": "europe-west1", "maxInstances": 10}}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"], "runtime": "nodejs20"}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "auth": {"port": 9099}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}